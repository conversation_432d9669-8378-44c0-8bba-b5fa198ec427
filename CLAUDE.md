# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Tauri-based desktop application called "mini-editor-pro" - a specialized tool for Android theme development and modification. The application provides a compact, always-on-top interface (220x420px) for various Android development tasks including APK manipulation, theme generation, color picking, and resource management.

## Commands

### Development
```bash
# Start development server
yarn tauri dev

# Build application
yarn tauri build

# Build with debug symbols
yarn tauri build --debug

# Lint TypeScript code
yarn lint

# Copy assets to build directories
yarn sync-assets
```

### Dependency Management
```bash
# Check outdated JavaScript dependencies
yarn outdated

# Update all JavaScript dependencies
yarn upgrade --latest

# Update Rust dependencies (in src-tauri directory)
cd src-tauri && cargo update
```

### Release Management
```bash
# Create and push version tag for GitHub release
VERSION="v0.2.2" && git tag $VERSION && git push origin $VERSION

# Clean up all local and remote tags
git tag | xargs -I {} sh -c 'git tag -d {} && git push --delete origin {}'
git fetch --prune --tags
```

## Architecture Overview

### Multi-Window System
The application uses a multi-window architecture with three main window types:
- **Main Window** (`index.html`): Compact 220x420px always-on-top toolbar
- **Search Color Window** (`search_color.html`): 1200x880px color picker and theme tools
- **Edit Config Window** (`edit_config.html`): 1200x860px configuration interface

### Frontend Structure
- **Entry Points**: Multiple HTML files in root directory, each with corresponding TypeScript modules
- **Main Logic**: `src/script/` contains all TypeScript modules organized by functionality
- **Asset Management**: `src/assets/` contains SVG icons, with automated copying via `scripts/copy-assets.js`
- **Styling**: Modular CSS files, including specialized stylesheets for color picker components

### Backend (Rust/Tauri)
- **Commands Module**: `src-tauri/src/commands.rs` - Core file operations and system utilities
- **Feature Modules**: Specialized modules for 9-patch compilation, theme packing, color processing
- **External Tools**: Integrated Android tools (aapt, adb) for APK manipulation
- **Platform Support**: Cross-platform binaries for Linux, macOS, and Windows

### Key Components

#### Color Picker System (`src/script/random_color_theme/`)
- **color_picker.ts**: Advanced magnifier-based color picker with smooth animations
- **search_color_tools.ts**: Color database management and search functionality
- **color-picker.css**: Specialized styling for magnifier and color info display
- Supports dynamic magnifier resizing with Ctrl key (200px → 400px)
- Implements pixel-perfect color sampling with device pixel ratio handling

#### Theme Generation (`src/script/random_color_theme/`)
- **random_color_theme.ts**: Automated theme generation based on color palettes
- **apply_random_theme.ts**: Theme application logic for Android systems
- Integrates with Android build tools for theme compilation

#### Configuration System (`src/script/edit_config/`)
- **edit_config_window.ts**: Configuration interface management
- Multiple config modules for different application features
- JSON5-based configuration files in `src-tauri/config/`

#### Window Management (`src/script/init_window.ts`)
- Cross-platform window positioning and state persistence
- Monitor-aware positioning with DPI scaling support
- Window size constants for consistent layout across features

### Build System
- **Vite Configuration**: Multi-entry build setup supporting multiple HTML pages
- **Asset Pipeline**: Automated SVG processing and path resolution
- **Tauri Integration**: Rust backend compilation with resource bundling
- **Development Server**: Hot reload with proper asset serving

### File Organization Patterns
- **Modular TypeScript**: Each feature in separate directory with related files
- **Rust Modules**: Feature-based organization with explicit module declarations
- **Configuration**: JSON5 files for structured settings management
- **Assets**: SVG-based icon system with automated processing

## Development Notes

### Color Picker Implementation
The color picker uses a sophisticated canvas-based magnifier system:
- Off-screen canvas for pixel sampling with `willReadFrequently: true`
- Device pixel ratio handling for accurate color capture
- Smooth animations using `requestAnimationFrame` with platform-specific smoothing
- Dynamic magnifier sizing while maintaining pixel accuracy

### Platform Considerations
- **Windows**: Custom cursor handling to prevent speed issues with `cursor: none`
- **macOS**: Shadow handling for window display issues
- **Cross-platform**: DPI scaling and monitor positioning logic

### Tauri-Specific Patterns
- Commands are defined in Rust and called from TypeScript via `@tauri-apps/api`
- File operations use Tauri's filesystem plugin for security
- Window management through Tauri's window API with state persistence
- Global shortcuts registered via Tauri's global-shortcut plugin

### CSS Architecture
- Component-specific stylesheets (e.g., `color-picker.css`)
- Utility classes in main stylesheets
- SVG icon integration with automated path resolution
- Responsive design considerations for different window sizes

## Important Files
- `src/script/init_window.ts`: Window initialization and positioning logic
- `src/script/random_color_theme/color_picker.ts`: Core color picker implementation
- `src-tauri/src/commands.rs`: Primary Rust command interface
- `vite.config.ts`: Build configuration for multi-page setup
- `src-tauri/tauri.conf.json5`: Application configuration and window settings
- `issues/color-picker-alignment-issues.md`: **Critical pixel alignment issues and solutions for color picker**

## Known Issues and Solutions
- **Color Picker Pixel Alignment**: See `issues/color-picker-alignment-issues.md` for detailed analysis of DPR-related pixel alignment issues and their solutions. This is essential reading for any AI working on color picker functionality.

## 记住，每次修复成功都记录在issues目录下的文档中，文档名和文件名保持一致。始终使用中文回复。


## 🧬 性能优先级处理策略

* **优先使用异步 IO + 并行调度**
* 合理利用多线程（根据 CPU 核心数自动调度）
* 任务允许多种手段混合以提升处理速度（前提是线程安全）
* 优化不能影响业务逻辑一致性

---

## 🛠️ Tauri 2 特殊说明

* `invoke` 方法来源：`@tauri-apps/api/core`
* 权限配置路径：`src-tauri/capabilities/default.json`（参考：[https://v2.tauri.org.cn/reference/acl/core-permissions）](https://v2.tauri.org.cn/reference/acl/core-permissions）)
* 包管理：`yarn`
* 严禁配置项（不得添加）：

```json
{"tauri": {"windows": [{"devtools": true}]}}
```

---

## 📁 文件系统代码示例

```ts
import { exists, BaseDirectory } from '@tauri-apps/plugin-fs';

// 检查 APPDATA 路径下是否存在 avatar.png 文件
const fileExists = await exists('avatar.png', { baseDir: BaseDirectory.AppData });
```

---

## ✅ 代码质量约束

* 第三方库必须查阅官方文档并验证其导出内容
* 禁止在 `.ts` 或 `.js` 文件中直接硬编码 CSS（除非动态设置必要）
* 新增样式需避免与现有类名冲突

---

## 🦀 Rust 代码约束

* 每次修改必须检查是否有编译错误
* 出现错误时先通读上下文再修复，不可盲目 patch
* 多次失败时可使用 `Context7` 联网查询 API 正确用法

---

## 🔧 代码优化约束

* 优化需建立在功能理解清晰基础上
* 禁止破坏功能一致性或误优化
* 重构应提升可读性、性能或维护性
