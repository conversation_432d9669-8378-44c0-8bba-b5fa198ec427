#!/bin/bash

# 演示数组模式的按键中断功能

TT_SCRIPT="./src-tauri/Terminal/tt"

echo "=== 数组模式按键中断功能演示 ==="
echo ""

# 检查 tt 脚本是否存在
if [ ! -f "$TT_SCRIPT" ]; then
    echo "错误: 找不到 tt 脚本: $TT_SCRIPT"
    exit 1
fi

# 确保脚本可执行
chmod +x "$TT_SCRIPT"

echo "功能说明："
echo "1. 在等待期间按任意键（除q外）可跳过当前等待"
echo "2. 按 'q' 键可退出整个批处理"
echo "3. 显示实时倒计时"
echo ""

echo "演示1: 自动完成（观察倒计时）"
echo "命令: $TT_SCRIPT '[demo1,demo2]' 3"
echo "说明: 让程序自然完成，观察倒计时显示"
echo ""
$TT_SCRIPT '[demo1,demo2]' 3

echo ""
echo "演示2: 手动测试中断功能"
echo "命令: $TT_SCRIPT '[manual1,manual2,manual3]' 5"
echo "说明: 5秒间隔，你可以尝试按键中断"
echo "  - 在等待期间按空格键跳过等待"
echo "  - 或按 'q' 键退出批处理"
echo ""
echo "按 Enter 开始手动测试..."
read

$TT_SCRIPT '[manual1,manual2,manual3]' 5

echo ""
echo "=== 演示完成 ==="
echo ""
echo "中断功能特性："
echo "✓ 实时倒计时显示"
echo "✓ 按任意键跳过等待"
echo "✓ 按 'q' 退出批处理"
echo "✓ 显示完成进度"
echo "✓ 优雅的用户交互"
