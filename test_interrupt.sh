#!/bin/bash

# 测试数组模式的按键中断功能

set -e

TT_SCRIPT="./src-tauri/Terminal/tt"

echo "=== 数组模式按键中断功能测试 ==="
echo ""

# 检查 tt 脚本是否存在
if [ ! -f "$TT_SCRIPT" ]; then
    echo "错误: 找不到 tt 脚本: $TT_SCRIPT"
    exit 1
fi

# 确保脚本可执行
chmod +x "$TT_SCRIPT"

echo "测试1: 基本中断功能演示"
echo "命令: $TT_SCRIPT '[test1,test2,test3]' 5"
echo "说明: 将启动一个5秒间隔的批处理，你可以："
echo "  - 按任意键（除q外）跳过当前等待"
echo "  - 按 'q' 退出整个批处理"
echo "  - 让它自然完成"
echo ""
echo "按 Enter 开始测试..."
read

echo "开始测试..."
$TT_SCRIPT '[test1,test2,test3]' 5

echo ""
echo "测试完成！"
echo ""

echo "测试2: 快速演示（2秒间隔）"
echo "命令: $TT_SCRIPT '[item1,item2]' 2"
echo "说明: 更短的间隔时间，便于快速测试中断功能"
echo ""
echo "按 Enter 开始测试..."
read

echo "开始测试..."
$TT_SCRIPT '[item1,item2]' 2

echo ""
echo "=== 中断功能测试完成 ==="
echo ""
echo "功能说明："
echo "✓ 按任意键（除q外）：跳过当前等待时间，立即处理下一个项目"
echo "✓ 按 'q' 键：退出整个批处理，显示已完成的项目数量"
echo "✓ 自然等待：让程序按设定的间隔时间正常执行"
echo "✓ 实时倒计时：显示剩余等待时间"
echo ""
echo "中断功能已准备就绪！"
