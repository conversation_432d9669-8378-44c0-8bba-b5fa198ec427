#!/bin/bash

# 数组模式测试脚本
# 用于验证 tt 命令行工具的数组模式功能

set -e

TT_SCRIPT="./src-tauri/Terminal/tt"

echo "=== TT 命令行工具数组模式测试 ==="
echo ""

# 检查 tt 脚本是否存在
if [ ! -f "$TT_SCRIPT" ]; then
    echo "错误: 找不到 tt 脚本: $TT_SCRIPT"
    exit 1
fi

# 确保脚本可执行
chmod +x "$TT_SCRIPT"

echo "1. 测试颜色数组模式（默认间隔2秒）"
echo "命令: $TT_SCRIPT '[color1,color2]'"
echo "---"
$TT_SCRIPT '[color1,color2]'
echo ""

echo "2. 测试图片数组模式（间隔1秒）"
echo "命令: $TT_SCRIPT '[icon.png,bg.9.png]' 1"
echo "---"
$TT_SCRIPT '[icon.png,bg.9.png]' 1
echo ""

echo "3. 测试无间隔模式（0秒）"
echo "命令: $TT_SCRIPT '[fast1,fast2,fast3]' 0"
echo "---"
$TT_SCRIPT '[fast1,fast2,fast3]' 0
echo ""

echo "4. 测试混合数组（颜色和图片）"
echo "命令: $TT_SCRIPT '[theme_color,icon.png,accent_color]' 1"
echo "---"
$TT_SCRIPT '[theme_color,icon.png,accent_color]' 1
echo ""

echo "5. 测试错误处理 - 无效间隔时间"
echo "命令: $TT_SCRIPT '[test1,test2]' -1"
echo "---"
if $TT_SCRIPT '[test1,test2]' -1 2>/dev/null; then
    echo "错误: 应该失败但成功了"
    exit 1
else
    echo "✓ 正确处理了无效间隔时间"
fi
echo ""

echo "6. 测试错误处理 - 空数组"
echo "命令: $TT_SCRIPT '[]' 1"
echo "---"
if $TT_SCRIPT '[]' 1 2>/dev/null; then
    echo "错误: 应该失败但成功了"
    exit 1
else
    echo "✓ 正确处理了空数组"
fi
echo ""

echo "7. 测试单项数组"
echo "命令: $TT_SCRIPT '[single_item]' 0"
echo "---"
$TT_SCRIPT '[single_item]' 0
echo ""

echo "8. 测试大数组（5个项目）"
echo "命令: $TT_SCRIPT '[item1,item2,item3,item4,item5]' 0"
echo "---"
$TT_SCRIPT '[item1,item2,item3,item4,item5]' 0
echo ""

echo "=== 所有测试完成 ==="
echo ""
echo "数组模式功能验证："
echo "✓ 颜色数组处理"
echo "✓ 图片数组处理"
echo "✓ 混合数组处理"
echo "✓ 间隔时间控制"
echo "✓ 错误处理机制"
echo "✓ 进度显示功能"
echo "✓ 成功率统计"
echo ""
echo "数组模式已准备就绪！"
