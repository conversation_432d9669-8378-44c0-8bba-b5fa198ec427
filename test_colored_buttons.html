<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>彩色按钮和分类标题测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-section {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
        }
        
        /* 模拟实际应用的样式 */
        .color-search-action-container {
            width: calc(100% - 6px);
            margin: 6px 3px;
            display: flex;
            flex-direction: column;
            gap: 4px;
        }
        
        .color-search-action-label {
            font-size: 10px;
            font-weight: 600;
            color: rgba(0, 0, 0, 0.7);
            margin-bottom: 2px;
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'Helvetica Neue', sans-serif;
        }
        
        .color-search-buttons-row {
            display: flex;
            flex-direction: row;
            flex-wrap: wrap;
            gap: 3px;
            align-items: center;
        }
        
        .color-search-action-btn {
            box-sizing: border-box;
            width: 48px;
            height: 24px;
            padding: 0;
            border-radius: 8px;
            border: 0.5px solid rgba(0, 0, 0, 0.08);
            transition: all 0.15s ease-out;
            cursor: pointer;
            margin: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            font-weight: 500;
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'Helvetica Neue', sans-serif;
            white-space: nowrap;
            flex-shrink: 0;
            background: rgba(242, 242, 247, 1);
            color: rgba(0, 0, 0, 0.8);
        }
        
        .color-search-action-btn:hover {
            transform: translateY(-1px) scale(1.02);
            border-color: rgba(0, 0, 0, 0.12);
        }
        
        .color-search-action-btn:active {
            transform: translateY(0px) scale(0.98);
            transition-duration: 0.05s;
        }
        
        /* 颜色值按钮 - 绿色系 */
        .color-search-action-color {
            background: rgba(52, 199, 89, 0.85);
            color: white;
            border-color: rgba(52, 199, 89, 0.3);
        }
        
        .color-search-action-color:hover {
            background: rgba(52, 199, 89, 1);
            border-color: rgba(52, 199, 89, 0.4);
        }
        
        .color-search-action-color:active {
            background: rgba(40, 180, 75, 1);
        }
        
        /* 图片按钮 - 橙色系 */
        .color-search-action-image {
            background: rgba(255, 149, 0, 0.85);
            color: white;
            border-color: rgba(255, 149, 0, 0.3);
        }
        
        .color-search-action-image:hover {
            background: rgba(255, 149, 0, 1);
            border-color: rgba(255, 149, 0, 0.4);
        }
        
        .color-search-action-image:active {
            background: rgba(230, 130, 0, 1);
        }
        
        /* 完整复制按钮 */
        .color-search-action-btn.full-copy {
            font-weight: 600;
            border-width: 1.5px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        
        .color-search-action-color.full-copy {
            background: rgba(52, 199, 89, 1);
            border-color: rgba(52, 199, 89, 0.5);
        }
        
        .color-search-action-color.full-copy:hover {
            background: rgba(40, 180, 75, 1);
            transform: translateY(-1px) scale(1.05);
        }
        
        .color-search-action-image.full-copy {
            background: rgba(255, 149, 0, 1);
            border-color: rgba(255, 149, 0, 0.5);
        }
        
        .color-search-action-image.full-copy:hover {
            background: rgba(230, 130, 0, 1);
            transform: translateY(-1px) scale(1.05);
        }
        
        .highlight {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 彩色按钮和分类标题测试</h1>
        <div class="highlight">
            <strong>新功能：</strong>
            <ul>
                <li>颜色值按钮使用绿色系背景</li>
                <li>图片按钮使用橙色系背景</li>
                <li>完整复制按钮更加突出</li>
                <li>分类标题显示每个按钮复制的文件数量</li>
            </ul>
        </div>
    </div>

    <div class="container">
        <h2>📊 按钮样式展示</h2>
        
        <div class="test-section">
            <h3>场景1：12个颜色值</h3>
            <div class="color-search-action-container">
                <div class="color-search-action-label">颜色值 (12个) - 全部12个 + 3个 + 3个 + 3个 + 3个</div>
                <div class="color-search-buttons-row">
                    <button class="color-search-action-btn color-search-action-color full-copy">全部</button>
                    <button class="color-search-action-btn color-search-action-color">1/4</button>
                    <button class="color-search-action-btn color-search-action-color">2/4</button>
                    <button class="color-search-action-btn color-search-action-color">3/4</button>
                    <button class="color-search-action-btn color-search-action-color">4/4</button>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>场景2：20个图片</h3>
            <div class="color-search-action-container">
                <div class="color-search-action-label">图片 (20个) - 全部20个 + 4个 + 4个 + 4个 + 4个 + 4个</div>
                <div class="color-search-buttons-row">
                    <button class="color-search-action-btn color-search-action-image full-copy">全部</button>
                    <button class="color-search-action-btn color-search-action-image">1/5</button>
                    <button class="color-search-action-btn color-search-action-image">2/5</button>
                    <button class="color-search-action-btn color-search-action-image">3/5</button>
                    <button class="color-search-action-btn color-search-action-image">4/5</button>
                    <button class="color-search-action-btn color-search-action-image">5/5</button>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>场景3：混合显示 - 8个颜色值 + 15个图片</h3>
            <div class="color-search-action-container">
                <div class="color-search-action-label">颜色值 (8个) - 全部8个 + 4个 + 4个</div>
                <div class="color-search-buttons-row">
                    <button class="color-search-action-btn color-search-action-color full-copy">全部</button>
                    <button class="color-search-action-btn color-search-action-color">1/2</button>
                    <button class="color-search-action-btn color-search-action-color">2/2</button>
                </div>
            </div>
            
            <div class="color-search-action-container">
                <div class="color-search-action-label">图片 (15个) - 全部15个 + 4个 + 4个 + 4个 + 3个</div>
                <div class="color-search-buttons-row">
                    <button class="color-search-action-btn color-search-action-image full-copy">全部</button>
                    <button class="color-search-action-btn color-search-action-image">1/4</button>
                    <button class="color-search-action-btn color-search-action-image">2/4</button>
                    <button class="color-search-action-btn color-search-action-image">3/4</button>
                    <button class="color-search-action-btn color-search-action-image">4/4</button>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>场景4：少量项目 - 3个颜色值</h3>
            <div class="color-search-action-container">
                <div class="color-search-action-label">颜色值 (3个) - 全部3个</div>
                <div class="color-search-buttons-row">
                    <button class="color-search-action-btn color-search-action-color full-copy">全部</button>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <h2>🎯 交互测试</h2>
        <div class="test-section">
            <p>请将鼠标悬停在按钮上测试悬停效果，点击按钮测试激活效果。</p>
            <p><strong>颜色说明：</strong></p>
            <ul>
                <li><span style="color: #34c759;">绿色系</span> - 颜色值按钮</li>
                <li><span style="color: #ff9500;">橙色系</span> - 图片按钮</li>
                <li><strong>加粗边框</strong> - 完整复制按钮</li>
            </ul>
        </div>
    </div>

    <script>
        // 添加点击事件来测试按钮功能
        document.addEventListener('DOMContentLoaded', function() {
            const buttons = document.querySelectorAll('.color-search-action-btn');
            
            buttons.forEach(button => {
                button.addEventListener('click', function() {
                    const isColor = this.classList.contains('color-search-action-color');
                    const isImage = this.classList.contains('color-search-action-image');
                    const isFull = this.classList.contains('full-copy');
                    const text = this.textContent;
                    
                    const type = isColor ? '颜色值' : '图片';
                    const copyType = isFull ? '全部' : `分段 ${text}`;
                    
                    console.log(`点击了 ${type} ${copyType} 按钮`);
                    
                    // 简单的视觉反馈
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 100);
                });
            });
            
            console.log('🎨 彩色按钮测试页面已加载');
            console.log('请点击按钮测试交互效果');
        });
    </script>
</body>
</html>
