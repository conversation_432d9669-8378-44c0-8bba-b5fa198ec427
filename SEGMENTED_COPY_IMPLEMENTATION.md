# 分段复制功能实现

## 🎯 需求说明
在复制颜色值和图片时，按照总数量进行智能划分。例如一共有20个符合条件的项目，那么复制按钮分为5个：1个完整复制 + 4个分段复制（每个5个项目）。

## ✅ 实现概述

### 核心功能
- **智能分段**：根据项目总数量自动计算最佳分段策略
- **灵活复制**：用户可以选择复制全部或复制特定分段
- **静默操作**：所有复制操作都是静默的，不显示消息提示

## 🔧 分段策略

### 分段规则
```typescript
≤4个项目   → 只有1个完整复制按钮
5-8个项目  → 1个完整 + 2个分段按钮
9-16个项目 → 1个完整 + 4个分段按钮
17-32个项目 → 1个完整 + 5个分段按钮
33个以上   → 1个完整 + 8个分段按钮
```

### 实际示例
- **3个颜色值**：`[复制颜色 (3个)]`
- **12个图片**：`[复制全部图片 (12个)] [复制图片 1/4 (3个)] [复制图片 2/4 (3个)] [复制图片 3/4 (3个)] [复制图片 4/4 (3个)]`
- **25个颜色值**：`[复制全部颜色 (25个)] [复制颜色 1/5 (5个)] [复制颜色 2/5 (5个)] ... [复制颜色 5/5 (5个)]`

## 🔧 技术实现

### 1. 核心方法：`createCopyButtonGroup`

```typescript
private createCopyButtonGroup(target: string, items: ColorSearchResult[], type: 'color' | 'image'): HTMLElement[] {
    const buttons: HTMLElement[] = [];
    const totalCount = items.length;
    const typeText = type === 'color' ? '颜色' : '图片';

    // 少量项目只创建完整复制按钮
    if (totalCount <= 4) {
        const button = this.createSingleCopyButton(
            `复制${typeText} (${totalCount}个)`,
            type,
            () => this.handleCopyCommand(target, items, type)
        );
        buttons.push(button);
        return buttons;
    }

    // 计算分段策略
    const segmentInfo = this.calculateSegments(totalCount);
    
    // 创建完整复制按钮
    const fullButton = this.createSingleCopyButton(
        `复制全部${typeText} (${totalCount}个)`,
        type,
        () => this.handleCopyCommand(target, items, type)
    );
    buttons.push(fullButton);

    // 创建分段复制按钮
    for (let i = 0; i < segmentInfo.segmentCount; i++) {
        const startIndex = i * segmentInfo.itemsPerSegment;
        const endIndex = Math.min(startIndex + segmentInfo.itemsPerSegment, totalCount);
        const segmentItems = items.slice(startIndex, endIndex);
        const actualCount = segmentItems.length;

        const segmentButton = this.createSingleCopyButton(
            `复制${typeText} ${i + 1}/${segmentInfo.segmentCount} (${actualCount}个)`,
            type,
            () => this.handleCopyCommand(target, segmentItems, type)
        );
        buttons.push(segmentButton);
    }

    return buttons;
}
```

### 2. 分段计算逻辑：`calculateSegments`

```typescript
private calculateSegments(totalCount: number): { segmentCount: number; itemsPerSegment: number } {
    if (totalCount <= 8) {
        return { segmentCount: 2, itemsPerSegment: Math.ceil(totalCount / 2) };
    } else if (totalCount <= 16) {
        return { segmentCount: 4, itemsPerSegment: Math.ceil(totalCount / 4) };
    } else if (totalCount <= 32) {
        return { segmentCount: 5, itemsPerSegment: Math.ceil(totalCount / 5) };
    } else {
        return { segmentCount: 8, itemsPerSegment: Math.ceil(totalCount / 8) };
    }
}
```

### 3. 按钮创建：`createSingleCopyButton`

```typescript
private createSingleCopyButton(text: string, type: 'color' | 'image', onClick: () => void): HTMLElement {
    const button = document.createElement('button');
    button.className = `color-search-action-btn color-search-action-${type}`;
    button.textContent = text;
    button.addEventListener('click', onClick);
    return button;
}
```

## 🎯 用户体验

### 按钮布局
- **完整复制按钮**：通常显示为第一个，样式可能更突出
- **分段按钮**：按顺序排列，清楚标明分段编号和项目数量
- **响应式布局**：按钮会根据容器宽度自动换行

### 按钮文本格式
- **完整复制**：`复制全部颜色 (20个)`
- **分段复制**：`复制颜色 1/4 (5个)`、`复制颜色 2/4 (5个)` 等
- **少量项目**：`复制颜色 (3个)`（不显示"全部"）

### 复制内容
每个按钮复制的都是标准的 tt 命令格式：
```bash
tt "item1,item2,item3" 10 2
```

## 📊 分段策略详解

### 设计原则
1. **避免过多按钮**：最多9个按钮（1个完整 + 8个分段）
2. **合理分段大小**：每段不少于2个项目，不多于10个项目
3. **用户友好**：分段数量和大小都易于理解和使用

### 具体策略表

| 项目总数 | 分段策略 | 按钮数量 | 每段大小 | 示例 |
|---------|---------|---------|---------|------|
| 1-4个 | 只有完整 | 1个 | - | `[复制颜色 (3个)]` |
| 5-8个 | 1完整+2分段 | 3个 | 2-4个 | `[全部(6个)] [1/2(3个)] [2/2(3个)]` |
| 9-16个 | 1完整+4分段 | 5个 | 2-4个 | `[全部(12个)] [1/4(3个)] [2/4(3个)] [3/4(3个)] [4/4(3个)]` |
| 17-32个 | 1完整+5分段 | 6个 | 3-7个 | `[全部(25个)] [1/5(5个)] [2/5(5个)] ... [5/5(5个)]` |
| 33个以上 | 1完整+8分段 | 9个 | 4-13个 | `[全部(50个)] [1/8(7个)] [2/8(6个)] ... [8/8(6个)]` |

## 🧪 测试验证

### 测试文件
- **`test_segmented_copy.html`** - 完整的分段复制功能测试页面

### 测试场景
1. **少量项目测试**：1-4个项目，验证只显示完整按钮
2. **中等数量测试**：5-32个项目，验证分段策略正确性
3. **大量项目测试**：33个以上项目，验证最大分段限制
4. **边界值测试**：4、8、16、32个项目的边界情况
5. **复制功能测试**：验证每个按钮复制的命令格式正确

### 验证要点
- ✅ 分段数量符合策略
- ✅ 每段项目数量合理
- ✅ 按钮文本格式正确
- ✅ 复制的命令格式标准
- ✅ 所有项目都被包含且无重复

## 🎉 优势总结

### 用户体验提升
- ✅ **灵活选择**：可以选择复制全部或部分项目
- ✅ **批量处理**：大量项目时可以分批处理，避免一次性处理过多
- ✅ **清晰标识**：每个按钮都清楚标明包含的项目数量
- ✅ **智能分段**：根据项目数量自动选择最佳分段策略

### 技术优势
- ✅ **算法优化**：分段策略经过优化，平衡了按钮数量和分段大小
- ✅ **代码复用**：复制逻辑统一，易于维护
- ✅ **扩展性好**：可以轻松调整分段策略
- ✅ **性能友好**：按钮创建和事件处理都很高效

### 实际应用价值
- ✅ **提高效率**：用户可以根据需要选择合适的复制范围
- ✅ **降低风险**：避免一次性处理过多项目导致的问题
- ✅ **增强控制**：用户对复制操作有更精细的控制
- ✅ **改善体验**：特别是在处理大量项目时，用户体验显著提升

现在用户在颜色搜索结果中会看到智能分段的复制按钮，可以根据需要选择复制全部或复制特定分段，大大提升了操作的灵活性和效率！
