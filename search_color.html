<!doctype html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8" />
    <link rel="stylesheet" href="./src/styles.css" />
    <link rel="stylesheet" href="./src/script/random_color_theme/search_color.css" />
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <!-- 加载JetBrains Mono字体（包含更多字重，用于英文和数字） -->
    <link href="https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600;700&display=swap"
        rel="stylesheet">
    <!-- 添加MiSans字体（用于中文） -->
    <link href="https://font.sec.miui.com/font/css?family=MiSans:400,500,600,700:Chinese_Simplify,Latin&display=swap"
        rel="stylesheet">
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>颜色搜索 - Mini Editor Pro</title>
</head>

<body class="color-search-window">
    <!-- 标题栏 -->
    <div data-tauri-drag-region id="title-bar" class="custom-title-bar" ondblclick="event.preventDefault()">
        <div id="title-bar-controls" class="title-bar-controls">
            <button id="close-btn">
                <img class="title_icon" id="close-icon" src="./assets/nofocus.svg" alt="close">
            </button>
            <button id="minimize-btn">
                <img class="title_icon" id="minimize-icon" src="./assets/nofocus.svg" alt="minimize">
            </button>
            <button id="maximize-btn">
                <img class="title_icon" id="maximize-icon" src="./assets/nofocus.svg" alt="maximize">
            </button>
        </div>
    </div>

    <!-- 添加截图按钮 - 静态HTML版本 -->
    <div class="add-screenshot-guide">
        <div class="screenshot-text">
            <div class="screenshot-section">
                <div class="screenshot-section-title">1、生成并应用随机颜色主题</div>
                <div class="screenshot-list">
                    <div class="screenshot-list-item">点击生成随机颜色主题按钮，选择模块并生成随机颜色主题</div>
                    <div class="screenshot-list-item">连接手机，应用对应版本，并在手机打开需要取色的页面，点击"添加截图"按钮</div>
                    <div class="screenshot-list-item">在下方列表选中需要取色的界面，鼠标移动到上方选中图片，点击对应位置即可获取颜色值id</div>
                </div>
            </div>

            <div class="screenshot-section">
                <div class="screenshot-section-title">2、设置/显示与亮度</div>
                <div class="screenshot-list">
                    <div class="screenshot-list-item">色彩风格：改为生动、真彩显示：关闭</div>
                    <div class="screenshot-list-item">部分机型不支持该功能可忽略</div>
                </div>
            </div>

            <div class="screenshot-section">
                <div class="screenshot-section-warning">3、安全警告</div>
                <div class="screenshot-list">
                    <div class="screenshot-warning-item">资源准确性可能存在误差</div>
                    <div class="screenshot-warning-item">建议在真机中测试获取到的资源是否能正常使用</div>
                    <div class="screenshot-warning-item">部分图片资源需要拉伸的情况下需要手动添加.9后缀</div>
                    <div class="screenshot-warning-item">随机颜色主题可能导致系统崩溃，并且需要清除所有数据才能恢复，请谨慎操作。</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 颜色范围搜索输入框 -->
    <div id="color-range-search-container" class="color-range-search-container">
        <input type="text" id="color-range-search-input" class="color-range-search-input"
            placeholder="输入颜色值进行范围搜索 (如: #D84059 20)" />
        <button id="color-range-search-btn" class="color-range-search-btn" title="执行颜色范围搜索">
            搜索
        </button>
    </div>

    <!-- 截图显示容器 -->
    <div id="screenshots-container" class="screenshots-container"></div>

    <div class="color-search-container">
        <div class="img-color-container">
        </div>

        <div class="color-value-container">
        </div>
    </div>


    <!-- 帮助界面 教程链接 -->
    <div id="help-container" class="help-container">
        <div class="help-content">

            <a class="help-link"
                href="https://zhuti.designer.xiaomi.com/docs/themePage/guide/#_1-%E6%96%B0%E5%BB%BA%E4%B8%BB%E9%A2%98">主题教程</a>
            <a class="help-link" href="https://zhuti.designer.xiaomi.com/docs/grammar/#aod-%E6%81%81%E5%B1%8F">息屏教程</a>
            <a class="help-link" href="https://zmsto9sfpq.feishu.cn/docx/doxcndMGffgwu1b5tQyDP5V77sf">小部件教程</a>
            <a class="help-link" href="https://egrqfhyh64.feishu.cn/docx/doxcnGtHAbjk2LuXdJ6ly6VqQhd">大图标教程</a>
            <a class="help-link" href="https://egrqfhyh64.feishu.cn/docx/EiNtdRwD1oTdEmxRIb8cqaqYnge">平板主题教程</a>
            <a class="help-link" href="https://zhuti.designer.xiaomi.com/">设计师后台</a>
            <a class="help-link" href="https://zhuti.designer.xiaomi.com/docs/grammar/">MAML 教程</a>
            <a class="help-link" href="https://si7pseb30t.feishu.cn/docx/QREXdMXFwoKyuvxrtVjcyQ0BnYe">主题制作常见问题</a>
            <a class="help-link"
                href="https://zmsto9sfpq.feishu.cn/docx/doxcndMGffgwu1b5tQyDP5V77sf#doxcneCuEG2Usq4aUKa7PxePWyc">小部件制作常见问题</a>
        </div>


    </div>


    <!-- 提示消息 -->
    <div id="message-container" class="message-container">
    </div>

    <!-- 初始化窗口 (必须在其他脚本之前加载) -->
    <script type="module" src="./src/script/init_window.ts"></script>
    <!-- 标题栏 -->
    <script type="module" src="./src/script/title_bar.ts"></script>
    <!-- 常用连接 -->
    <script type="module" src="./src/script/help.ts"></script>
    <!-- 用户名 -->
    <script type="module" src="./src/script/user_name.ts"></script>
    <!-- 当前平台 -->
    <script type="module" src="./src/script/current_platform.ts"></script>

    <!-- 环境变量 -->
    <script type="module" src="./src/script/fix_env_vars.ts"></script>
    <!-- 截屏 -->
    <script type="module" src="./src/script/screenshot.ts"></script>
    <!-- 获取根目录 -->
    <script type="module" src="./src/script/get_root_dir.ts"></script>
    <!-- 生成随机主题 -->
    <script type="module" src="./src/script/random_color_theme/random_color_theme.ts"></script>
    <!-- 颜色搜索窗口 -->
    <script type="module" src="./src/script/random_color_theme/search_color_tools.ts"></script>
    <!-- 颜色拾取器 (放在最后加载) -->
    <script type="module" src="./src/script/random_color_theme/color_picker.ts"></script>
</body>

</html>