/* 历史记录样式文件 - history.css */

/* 历史记录容器基础样式 */
.history-container {
    display: none;
    position: absolute;
    top: 94px;
    left: 0px;
    flex-direction: column;
    z-index: 999999;
    /* border-radius: 8px; */
    background: #fff9;
    backdrop-filter: blur(10px);
    padding: 0;
    margin: 0;
    box-sizing: border-box;
    width: calc(100vw);
    min-height: calc(100vh - 120px);
    max-height: calc(100vh - 120px);
    overflow-y: auto;
    /* box-shadow: 0 20px 80px rgba(0, 0, 0, 0.5); */
    /* 隐藏滚动条 */
    scrollbar-width: none;
    /* Firefox */
    -ms-overflow-style: none;
    padding: 12px;
    /* outline: 1px solid #0002; */
    /* 优化动画性能 */
    will-change: opacity, transform;
    transform: translateZ(0);
    /* 启用硬件加速 */
    transition: all 0.3s ease !important;
}

/* Chrome, Safari and Opera */
.history-container::-webkit-scrollbar {
    display: none;
}

/* 打开动画 */
.history-container.opening {
    opacity: 0;
    transform: translateY(-60px) scale(0.5) translateZ(0);
    transition: opacity 0.1s ease-out, transform 0.1s ease-out;
}

.history-container.open {
    opacity: 1;
    transform: translateY(0) scale(1) translateZ(0);
}

/* 关闭动画 */
.history-container.closing {
    opacity: 0;
    transform: translateY(-60px) scale(0.5) translateZ(0);
    transition: opacity 0.1s ease-in, transform 0.1s ease-in;
}

/* 历史记录列表项样式 */
.history-list {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 6px 4px;
    padding: 0px 4px;
    position: relative;
    overflow: hidden;
    opacity: 1;
    /* 默认可见，移除入场动画 */
    font-size: 10px;
    transform: translateY(0) scale(1);
    /* 默认位置 */
    transition: background-color 0.2s ease, transform 0.2s ease;
    border-radius: 5px;
    min-height: 30px;
    max-height: 30px;
    background-color: transparent;
    will-change: transform, background-color;
    background-color: #fff0;
    outline: 1px solid #0002;
    /* 减少will-change属性 */
}

/* 高亮匹配项 */
.history-list.highlighted {
    background-color: #4169e133 !important;
    transform: scale(1.02);
}

/* 悬停效果 */
.history-list.hovered {
    background-color: #fff;
    transform: scale(1.03);
}

/* 删除动画相关的样式 */
.history-list.deleting {
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    transform: translateX(100%);
    opacity: 0;
    height: 0 !important;
    min-height: 0 !important;
    max-height: 0 !important;
    padding: 0 !important;
    margin: 0 !important;
    background-color: transparent !important;
    pointer-events: none;
}

/* 文本容器样式 */
.history-list .text-container {
    padding: 0 0px 0 10px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    position: relative;
    z-index: 1;
    transition: color 0.3s ease;
    font-family: system-ui, -apple-system, sans-serif;
}

/* 背景层样式 */
.background-layer,
.history-list .background-layer {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    height: 100%;
    background: linear-gradient(90deg, #4169e1, #00bfff);
    border-radius: 5px;
    transition: all 0.3s ease;
    opacity: 0;
    z-index: 0;
}

/* 删除按钮样式 */
.del_button {
    background: transparent;
    color: #ff4757;
    border: none;
    padding: 6px 12px;
    font-size: 10px;
    cursor: pointer;
    transition: color 0.2s ease, background-color 0.2s ease, transform 0.2s ease;
    min-width: 45px;
    min-height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    position: relative;
    z-index: 10;
}

.del_button:hover {
    transform: translateY(0px) scale(1.1);
}

/* 隐藏非div元素 */
.history-container>*:not(div) {
    display: none;
}

/* 暗色主题适配 */
@media (prefers-color-scheme: dark) {
    .history-container {
        background: #000c !important;
    }

    .history-list {
        background-color: #fff0;
        outline: 1px solid #fff2;

    }

    .history-list.hovered {
        background-color: #fff1;
        outline: 1px solid #fff4;
        transform: scale(1.03);
    }
}

/* 注释掉的备用样式 */
/*
.history-list:not(:hover):nth-child(odd) {
    background-color: #00000009;
    transition: background-color 0.3s ease, transform 0.5s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.history-list:not(:hover):nth-child(even) {
    background-color: transparent;
    transition: background-color 0.3s ease, transform 0.5s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}
*/