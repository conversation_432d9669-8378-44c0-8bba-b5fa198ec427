/* 保持原始黑色按钮样式不变 */
.special-overlay-btn {
    position: fixed;
    top: 0;
    right: 0;
    width: 15px;
    height: 30px;
    background-color: #00000000;
    border: none;
    z-index: 9999999;
    cursor: pointer !important;
    font-size: 14px;
    font-weight: bold;
    text-align: center;
    line-height: 40px;
    user-select: none;
    /* 基础过渡效果 */
    transition: all 0.1s ease-out;
}

/* 鼠标悬停时确保显示手指指针 */
.special-overlay-btn:hover {
    cursor: pointer !important;
}

/* 现代化覆盖层 - 使用flexbox确保完美居中 */
.special-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 99998;

    /* 简洁的背景效果 */
    background: rgba(248, 248, 248, 0.85);
    backdrop-filter: blur(20px) saturate(120%);

    /* 使用flexbox实现完美居中 */
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    margin: 0;

    /* 确保内容不会超出屏幕 */
    box-sizing: border-box;
    overflow: hidden;

    /* 优雅的淡入动画 */
    animation: overlayFadeIn 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

/* 按钮容器 - 简化布局，依赖父容器的flexbox居中 */
.special-button-container {
    width: 240px;
    display: flex;
    align-items: center;
    flex-direction: column;
    gap: 20px;
    padding: 20px 0;
}

/* 覆盖层淡入动画 */
@keyframes overlayFadeIn {
    from {
        opacity: 0;
        backdrop-filter: blur(0px) saturate(100%);
    }

    to {
        opacity: 1;
        backdrop-filter: blur(20px) saturate(120%);
    }
}

/* 简洁优雅的 macOS 按钮设计 */
.overlay-button {
    box-sizing: border-box;
    /* 按钮尺寸 - 确保适合屏幕且居中显示 */
    width: 90%;
    height: 32px;
    padding: 0 12px;
    /* macOS 经典圆角 */
    border-radius: 8px;
    border: 0.5px solid rgba(0, 0, 0, 0.08);
    /* 简洁的背景色 */
    background: rgba(255, 255, 255, 0.9);
    color: #1d1d1f;
    /* 平滑过渡 */
    transition: all 0.15s ease-out;
    /* 确保按钮不会有额外的margin影响居中 */
    margin: 0;
}

/* 简洁的悬停效果 - 优化视觉效果 */
.overlay-button:hover {
    background: rgba(245, 245, 247, 0.95);
    border-color: rgba(0, 0, 0, 0.1);
    /* 使用transform而不是margin，避免影响布局 */
    transform: translateY(-1px) scale(1.02);
}

/* 简洁的点击效果 */
.overlay-button:active {
    background: rgba(230, 230, 230, 0.9);
    /* 重置transform */
    transform: translateY(0px) scale(0.98);

    transition-duration: 0.05s;
}

/* 简洁的焦点环 */
.overlay-button:focus {
    box-shadow:
        0 1px 3px rgba(0, 0, 0, 0.1),
        0 2px 6px rgba(0, 0, 0, 0.05),
        inset 0 0.5px 0 rgba(255, 255, 255, 0.8),
        0 0 0 3px rgba(0, 122, 255, 0.3);
}

/* 简洁的关闭按钮 */
.overlay-button.close-button {
    background: rgba(255, 69, 58, 0.9);
    border-color: rgba(255, 59, 48, 0.3);
    color: white;
    font-weight: 600;
    box-shadow:
        0 1px 3px rgba(255, 59, 48, 0.2),
        0 2px 6px rgba(255, 59, 48, 0.1),
        inset 0 0.5px 0 rgba(255, 255, 255, 0.3);
}

/* 关闭按钮悬停效果 */
.overlay-button.close-button:hover {
    background: rgba(255, 79, 68, 0.95);
    border-color: rgba(255, 59, 48, 0.4);
    /* 使用transform确保一致性 */
    transform: translateY(-1px) scale(1.02);
    box-shadow:
        0 2px 4px rgba(255, 59, 48, 0.25),
        0 4px 8px rgba(255, 59, 48, 0.15),
        inset 0 0.5px 0 rgba(255, 255, 255, 0.4);
}

/* 关闭按钮点击效果 */
.overlay-button.close-button:active {
    background: rgba(235, 49, 38, 0.9);
    /* 重置transform */
    transform: translateY(0px) scale(0.98);
    box-shadow:
        0 1px 2px rgba(255, 59, 48, 0.3),
        inset 0 1px 2px rgba(0, 0, 0, 0.2);
    transition-duration: 0.05s;
}

/* 关闭按钮焦点环 */
.overlay-button.close-button:focus {
    box-shadow:
        0 1px 3px rgba(255, 59, 48, 0.2),
        0 2px 6px rgba(255, 59, 48, 0.1),
        inset 0 0.5px 0 rgba(255, 255, 255, 0.3),
        0 0 0 3px rgba(255, 59, 48, 0.4);
}

/* ========== macOS 极简设备选择样式 ========== */

/* 设备项目容器 - 极简列表风格 */
.device-item {
    box-sizing: border-box;
    height: 32px;
    padding: 0 12px;
    border-radius: 4px;
    border: none;
    background: rgba(255, 255, 255, 0.85);
    transition: all 0.1s ease-out;
    cursor: pointer;
    margin: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: 0 0.5px 1px rgba(0, 0, 0, 0.03);
}

/* 设备项目悬停效果 */
.device-item:hover {
    background: rgba(245, 245, 247, 0.9);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.06);
}

/* 设备项目激活效果 */
.device-item:active {
    transform: scale(0.98);
    transition-duration: 0.05s;
}

/* 无权限设备项目样式 */
.device-item.disabled {
    background: rgba(248, 248, 248, 0.6);
    cursor: not-allowed;
    opacity: 0.4;
}

.device-item.disabled:hover {
    transform: none;
    background: rgba(248, 248, 248, 0.6);
    box-shadow: 0 0.5px 1px rgba(0, 0, 0, 0.03);
}

/* 设备主信息容器 */
.device-main-info {
    display: flex;
    align-items: center;
    flex: 1;
    min-width: 0;
}

/* 设备名称 */
.device-name-simple {
    font-size: 9px;
    font-weight: 500;
    color: #000;
    line-height: 1.2;
    white-space: nowrap;
    font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 无权限设备名称颜色 */
.device-item.disabled .device-name-simple {
    color: #8e8e93;
}

/* 环境信息容器 */
.device-secondary-info {
    display: flex;
    align-items: center;
    flex-shrink: 0;
}

/* 环境标签 - 无背景极简版 */
.device-env-tag {
    padding: 0;
    border-radius: 0;
    font-size: 9px;
    font-weight: 500;
    line-height: 1;
    background: none;
}

/* 环境标签颜色 - 纯文字色彩 */
.device-env-tag {
    color: #007aff;
}

.device-env-tag.no-permission {
    color: #8e8e93;
}

/* 设备项目容器布局调整 - 与环境按钮保持一致的边距 */
.special-button-container .device-item {
    width: 100%;
    margin: 0;
}

/* 统一容器内所有按钮的宽度 */
.special-button-container .overlay-button {
    width: 100%;
    margin: 0;
}

/* 关闭按钮样式 - 与环境按钮保持一致的边距 */
.special-button-container .overlay-button.close-button {
    width: 100%;
    margin: 0;
    height: 32px;
    font-size: 12px;
}

.overlay-title {
    margin: 0 5% 10px 5%;
    font-size: 11px;
    font-weight: 500;
    color: #1d1d1f;
    text-align: center;
}