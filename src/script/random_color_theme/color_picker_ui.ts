/**
 * 颜色取色器UI组件管理模块
 * 负责放大镜、颜色信息显示、DOM元素创建和样式设置
 */

import { getPlatformConfig, transparentCursor } from './color_picker_config';
import { getIsDatabaseLoaded, findColorByHex } from './color_database';

/**
 * UI元素接口定义
 */
export interface ColorPickerUIElements {
    overlay: HTMLDivElement;
    eventCatcher: HTMLDivElement;
    magnifier: HTMLDivElement;
    canvas: HTMLCanvasElement;
    colorInfoElement: HTMLDivElement;
    screenCanvas: HTMLCanvasElement;
    tempCanvas: HTMLCanvasElement;
    screenCtx: CanvasRenderingContext2D;
    tempCtx: CanvasRenderingContext2D;
}

/**
 * 创建全屏透明覆盖层
 */
export function createOverlay(): HTMLDivElement {
    const overlay = document.createElement('div');
    overlay.id = 'color-picker-overlay';
    overlay.style.position = 'fixed';
    overlay.style.top = '0';
    overlay.style.left = '0';
    overlay.style.width = '100vw';
    overlay.style.height = '100vh';
    overlay.style.zIndex = '9999';
    overlay.style.backgroundColor = 'transparent';
    overlay.style.pointerEvents = 'none';

    document.body.appendChild(overlay);
    return overlay;
}

/**
 * 创建事件捕获层
 */
export function createEventCatcher(): HTMLDivElement {
    const eventCatcher = document.createElement('div');
    eventCatcher.id = 'color-picker-click-catcher';
    eventCatcher.style.position = 'fixed';
    eventCatcher.style.top = '0';
    eventCatcher.style.left = '0';
    eventCatcher.style.width = '100vw';
    eventCatcher.style.height = '100vh';
    eventCatcher.style.zIndex = '9998';
    eventCatcher.style.backgroundColor = 'transparent';
    eventCatcher.style.pointerEvents = 'auto';

    // 使用自定义透明光标替代none，避免在Windows上鼠标速度变快的问题
    eventCatcher.style.cursor = transparentCursor;

    document.body.appendChild(eventCatcher);
    document.body.style.cursor = transparentCursor;

    return eventCatcher;
}

/**
 * 创建放大镜元素和画布
 */
export function createMagnifier(finalTransition: string): { magnifier: HTMLDivElement, canvas: HTMLCanvasElement } {
    const config = getPlatformConfig();

    // 创建放大镜元素
    const magnifier = document.createElement('div');
    magnifier.id = 'color-picker-magnifier';
    magnifier.style.position = 'fixed';

    // 先定义放大镜大小变量，确保一致性
    const magnifierWidth = config.magnifierSize;
    const magnifierHeight = config.magnifierSize;

    // 使用平台配置来设置放大镜大小 - 使用!important确保大小不被覆盖
    magnifier.style.cssText = `
        position: fixed !important;
        width: ${magnifierWidth}px !important;
        height: ${magnifierHeight}px !important;
        border-radius: 50% !important;
        border: 2px solid rgba(255, 255, 255, 0.5) !important;
        pointer-events: none !important;
        z-index: 10000 !important;
        overflow: hidden !important;
        display: none !important;
        transform: translate(-50%, -50%) !important;
        box-sizing: content-box !important;
    `;

    // 在Windows平台上添加过渡效果使移动更顺滑
    magnifier.style.transition = finalTransition;

    // 添加阴影效果，使放大镜更加明显
    magnifier.style.boxShadow = '0 0 10px rgba(0, 0, 0, 0.5)';

    // 创建放大镜内的画布
    const canvas = document.createElement('canvas');
    // 设置画布大小与放大镜大小匹配，使用双倍分辨率提高清晰度
    canvas.width = magnifierWidth * 2;
    canvas.height = magnifierHeight * 2;
    // 使用cssText确保样式不被覆盖
    canvas.style.cssText = `
        position: absolute !important;
        left: 0 !important;
        top: 0 !important;
        width: 100% !important;
        height: 100% !important;
        box-sizing: border-box !important;
    `;

    // 添加组件到放大镜
    magnifier.appendChild(canvas);
    document.body.appendChild(magnifier);

    return { magnifier, canvas };
}

/**
 * 创建颜色信息显示元素
 */
export function createColorInfoElement(): HTMLDivElement {
    const colorInfoElement = document.createElement('div');
    colorInfoElement.id = 'color-picker-info';
    colorInfoElement.style.position = 'fixed';
    colorInfoElement.style.backgroundColor = 'transparent'; // 移除背景色
    colorInfoElement.style.color = 'white';
    colorInfoElement.style.padding = '4px 8px';
    colorInfoElement.style.fontSize = '16px';
    colorInfoElement.style.fontFamily = 'monospace';
    colorInfoElement.style.fontWeight = 'bold'; // 加粗文字
    colorInfoElement.style.pointerEvents = 'none';
    colorInfoElement.style.zIndex = '10001';
    colorInfoElement.style.textAlign = 'center';
    colorInfoElement.style.textShadow = '-1px -1px 0 #000, 1px -1px 0 #000, -1px 1px 0 #000, 1px 1px 0 #000'; // 黑色描边
    colorInfoElement.style.display = 'none';

    document.body.appendChild(colorInfoElement);
    return colorInfoElement;
}

/**
 * 创建屏幕截图画布
 */
export function createScreenCanvas(): { screenCanvas: HTMLCanvasElement, screenCtx: CanvasRenderingContext2D } {
    const screenCanvas = document.createElement('canvas');
    const screenCtx = screenCanvas.getContext('2d', { willReadFrequently: true });
    if (!screenCtx) {
        throw new Error('无法创建屏幕Canvas上下文');
    }
    screenCanvas.style.display = 'none';
    document.body.appendChild(screenCanvas);

    return { screenCanvas, screenCtx };
}

/**
 * 创建临时画布用于放大镜
 */
export function createTempCanvas(): { tempCanvas: HTMLCanvasElement, tempCtx: CanvasRenderingContext2D } {
    const tempCanvas = document.createElement('canvas');
    const tempCtx = tempCanvas.getContext('2d', { willReadFrequently: true });
    if (!tempCtx) {
        throw new Error('无法创建临时Canvas上下文');
    }

    return { tempCanvas, tempCtx };
}

/**
 * 创建所有UI元素
 */
export function createAllUIElements(finalTransition: string): ColorPickerUIElements {
    const overlay = createOverlay();
    const eventCatcher = createEventCatcher();
    const { magnifier, canvas } = createMagnifier(finalTransition);
    const colorInfoElement = createColorInfoElement();
    const { screenCanvas, screenCtx } = createScreenCanvas();
    const { tempCanvas, tempCtx } = createTempCanvas();

    return {
        overlay,
        eventCatcher,
        magnifier,
        canvas,
        colorInfoElement,
        screenCanvas,
        tempCanvas,
        screenCtx,
        tempCtx
    };
}

/**
 * 更新放大镜尺寸
 */
export function updateMagnifierSize(
    magnifier: HTMLDivElement,
    isControlPressed: boolean
): { currentMagnifierSize: number, currentCaptureSize: number } {
    const config = getPlatformConfig();
    const targetSize = isControlPressed ? config.magnifierSizeExpanded : config.magnifierSize;
    const targetCapture = isControlPressed ? config.captureSizeExpanded : config.captureSize;

    // 立即应用目标尺寸
    const currentMagnifierSize = targetSize;
    const currentCaptureSize = targetCapture;

    if (magnifier) {
        // 移除过渡效果，确保尺寸立即变化
        magnifier.style.transition = 'none';

        magnifier.style.width = `${targetSize}px`;
        magnifier.style.height = `${targetSize}px`;
        const canvasElement = magnifier.querySelector('canvas');
        if (canvasElement) {
            canvasElement.width = targetSize * 2;
            canvasElement.height = targetSize * 2;
        }
    }

    return { currentMagnifierSize, currentCaptureSize };
}

// 缓存上一次的颜色值和查询结果
let lastColorHex = '';
let lastFoundName = '';
let lastFoundTarget = '';
let lastColorInfoElement: HTMLElement | null = null;

/**
 * 更新颜色信息显示
 */
export function updateColorInfo(
    colorInfoElement: HTMLDivElement,
    x: number,
    y: number,
    colorHex: string,
    currentMagnifierSize: number
) {
    if (!colorInfoElement) return;

    try {
        // 检查是否与上次颜色相同
        const isSameColor = (colorHex === lastColorHex);

        // 初始化颜色名称和目标
        let colorName = '';
        let colorTarget = '';

        // 如果颜色相同，直接使用缓存的结果
        if (isSameColor) {
            colorName = lastFoundName;
            colorTarget = lastFoundTarget;
        } else {
            // 更新上次颜色记录
            lastColorHex = colorHex;

            // 查找颜色数据库中对应的值
            const colorKey = colorHex.toUpperCase();

            // 只有当数据库成功加载时才尝试查找颜色信息
            if (getIsDatabaseLoaded()) {
                const colorData = findColorByHex(colorKey);
                if (colorData) {
                    colorName = colorData.name || '';
                    colorTarget = colorData.target || '';

                    // 缓存找到的结果
                    lastFoundName = colorName;
                    lastFoundTarget = colorTarget;
                } else {
                    // 清空缓存
                    lastFoundName = '';
                    lastFoundTarget = '';
                }
            } else {
                // 数据库未加载，清空缓存
                lastFoundName = '';
                lastFoundTarget = '';
            }
        }

        // 始终显示hex值，如果找到颜色数据则同时显示名称和目标
        let colorText = '';

        // 始终显示hex值
        colorText += `<div style="margin-bottom: 3px; text-align: center; font-weight: bold;">${colorHex}</div>`;

        if (colorName) {
            colorText += `<div style="margin-bottom: 2px; text-align: center; font-size: 11px; opacity: 0.9;">${colorName}</div>`;
        }

        if (colorTarget) {
            colorText += `<div style="text-align: center; font-size: 10px; opacity: 0.8;">${colorTarget}</div>`;
        }

        // 如果没有内容，就不显示
        if (!colorText.trim()) {
            colorInfoElement.style.display = 'none';
            return;
        }

        // 只在内容变化时更新DOM
        if (colorInfoElement.innerHTML !== colorText) {
            colorInfoElement.innerHTML = colorText;
        }

        // 只在首次设置或元素变化时应用样式
        if (lastColorInfoElement !== colorInfoElement) {
            // 设置文字样式
            colorInfoElement.style.fontFamily = 'ui-monospace, SFMono-Regular, "JetBrainsMono", "JetBrains Mono", Menlo, Monaco, Consolas, monospace';
            colorInfoElement.style.color = 'white';
            colorInfoElement.style.fontSize = '12px';
            colorInfoElement.style.textShadow = '';
            colorInfoElement.style.setProperty('-webkit-text-stroke', '0');

            // 设置精致的、带有现代设计感的背景板样式
            colorInfoElement.style.backgroundColor = 'rgba(30, 30, 30, 0.7)';
            colorInfoElement.style.backdropFilter = 'blur(10px) saturate(180%)';
            colorInfoElement.style.setProperty('-webkit-backdrop-filter', 'blur(10px) saturate(180%)');
            colorInfoElement.style.borderRadius = '8px';
            colorInfoElement.style.padding = '5px 10px';
            colorInfoElement.style.boxShadow = '0 4px 15px rgba(0, 0, 0, 0.2)';
            colorInfoElement.style.border = '1px solid rgba(255, 255, 255, 0.1)';

            lastColorInfoElement = colorInfoElement;
        }

        // 计算位置（仅在位置变化时更新）
        const textTopPosition = y + (currentMagnifierSize / 2) + 15;

        colorInfoElement.style.transform = '';
        colorInfoElement.style.width = 'auto';
        colorInfoElement.style.display = 'block';

        // 使用requestAnimationFrame优化位置更新
        requestAnimationFrame(() => {
            if (!colorInfoElement) return;

            const width = colorInfoElement.offsetWidth;
            const idealLeft = x - width / 2;
            const padding = 10;
            let finalLeft = Math.min(idealLeft, window.innerWidth - width - padding);
            finalLeft = Math.max(finalLeft, padding);

            colorInfoElement.style.left = `${finalLeft}px`;
            colorInfoElement.style.top = `${textTopPosition}px`;
        });

    } catch (error) {
        console.error('更新颜色信息显示出错:', error);
    }
}

/**
 * 清理所有UI元素
 */
export function cleanupUIElements(elements: ColorPickerUIElements) {
    const elementsToRemove = [
        { element: elements.overlay, name: 'overlay' },
        { element: elements.eventCatcher, name: 'eventCatcher' },
        { element: elements.magnifier, name: 'magnifier' },
        { element: elements.colorInfoElement, name: 'colorInfoElement' },
        { element: elements.screenCanvas, name: 'screenCanvas' },
        { element: elements.tempCanvas, name: 'tempCanvas' }
    ];

    for (const { element, name } of elementsToRemove) {
        try {
            if (element && document.body.contains(element)) {
                document.body.removeChild(element);
            }
        } catch (error) {
            console.error(`移除${name}元素失败:`, error);
        }
    }
}

/**
 * 恢复鼠标样式
 */
export function restoreMouseCursor() {
    try {
        document.body.style.cursor = '';

        // 恢复所有元素的鼠标样式
        const allElements = document.getElementsByTagName('*');
        for (let i = 0; i < allElements.length; i++) {
            const element = allElements[ i ];
            if (element instanceof HTMLElement) {
                // 检查自定义光标(url开头)或none光标，并恢复默认样式
                if (element.style.cursor === 'none' ||
                    element.style.cursor.startsWith('url(') ||
                    element.style.cursor.includes('transparent')) {
                    element.style.cursor = '';
                }
            }
        }
    } catch (error) {
        console.error('恢复鼠标样式失败:', error);
    }
}