/**
 * 颜色取色器自动化和快捷键管理模块
 * 负责快捷键注册、预览区域事件绑定和自动激活逻辑
 */

import { messageError } from '../prompt_message';
import { register, unregisterAll } from '@tauri-apps/plugin-global-shortcut';
import {
    colorPickerLocked,
    setColorPickerLocked,
    globalEscapeKeyHandler
} from './color_picker_config';
import { resetColorPickerState } from './color_picker_core';

/**
 * 注册取色器快捷键
 * 初始化获取取色器的快捷键
 */
export async function registerColorPickerShortcut(colorPickerFunction: () => Promise<string | undefined>): Promise<boolean> {
    try {
        // 先清空所有快捷键
        try {
            await unregisterAll();
        } catch (e) {
            console.warn('清空快捷键失败，可能是首次运行:', e);
        }

        // 注册新的快捷键,确保松手后触发
        try {
            await register('Control+Shift+C', async () => {
                // 检查窗口是否激活以及鼠标是否在窗口内
                if (!document.hasFocus()) {
                    return;
                }

                if (!window._isMouseInWindow) {
                    return;
                }

                // 使用 setTimeout 确保在按键释放后执行
                setTimeout(async () => {
                    // 如果有下拉菜单展开，阻止取色器激活
                    if (window._dropdownMenuOpen) {
                        return;
                    }

                    // 检查是否有实际显示的下拉菜单
                    const allDropdowns = document.querySelectorAll('.apply-theme-dropdown-menu');
                    for (const dropdown of allDropdowns) {
                        const element = dropdown as HTMLElement;
                        if (element.style.display !== 'none' && element.style.display !== '') {
                            return;
                        }
                    }

                    // 如果取色器已经激活，不要重复激活
                    if (window._colorPickerActive) {
                        return;
                    }

                    try {
                        // 直接等待取色结果，而不是嵌套then回调
                        await colorPickerFunction();
                        // 取色器自己会调用cleanup，不需要额外的清理
                    } catch (error) {
                        messageError(`取色器执行错误: ${error}`);
                        // 确保在错误情况下也重置状态
                        window._colorPickerActive = false;
                    }
                }, 100);
            });
            return true;
        } catch (e) {
            console.error('快捷键注册失败:', e);
            return false;
        }
    } catch (error) {
        console.error('注册快捷键过程中出错:', error);
        return false;
    }
}

/**
 * 手动激活取色器
 * 可以通过按钮或其他UI元素调用此函数
 */
export async function activateColorPicker(colorPickerFunction: () => Promise<string | undefined>): Promise<string | undefined> {
    // 如果有下拉菜单展开，阻止取色器激活
    if (window._dropdownMenuOpen) {
        return undefined;
    }

    // 检查是否有实际显示的下拉菜单
    const allDropdowns = document.querySelectorAll('.apply-theme-dropdown-menu');
    for (const dropdown of allDropdowns) {
        const element = dropdown as HTMLElement;
        if (element.style.display !== 'none' && element.style.display !== '') {
            return undefined;
        }
    }

    // 确保重置取色器状态
    resetColorPickerState();

    try {
        const color = await colorPickerFunction();
        return color;
    } catch (error) {
        console.error('手动激活取色器失败:', error);
        return undefined;
    }
}

/**
 * 预览图鼠标进入处理函数
 */
export async function onPreviewMouseEnter(
    activateColorPickerFn: () => Promise<string | undefined>
): Promise<void> {
    // 如果有下拉菜单展开，阻止取色器激活
    if (window._dropdownMenuOpen) {
        return;
    }

    // 检查是否有实际显示的下拉菜单
    const allDropdowns = document.querySelectorAll('.apply-theme-dropdown-menu');
    for (const dropdown of allDropdowns) {
        const element = dropdown as HTMLElement;
        if (element.style.display !== 'none' && element.style.display !== '') {
            return;
        }
    }

    // 如果取色器处于强制锁定状态，完全忽略所有预览区域事件
    if (window._colorPickerForceLocked) {
        return;
    }

    // 如果已经锁定，直接返回
    if (colorPickerLocked) {
        return;
    }

    // 如果取色器已经激活，不要重复触发
    if (window._colorPickerActive) {
        return;
    }

    try {
        // 激活取色器前锁定，防止误触发离开事件
        setColorPickerLocked(true);

        // 立即激活取色器，不添加延迟
        await activateColorPickerFn();
    } catch (error) {
        console.error('自动激活取色器失败:', error);
        // 发生错误时解除锁定
        setColorPickerLocked(false);
    }
}

/**
 * 预览图鼠标离开处理函数 - 确保立即响应
 */
export function onPreviewMouseLeave(event: Event): void {
    // 如果取色器处于强制锁定状态但时间超过300ms，则不再忽略离开事件
    // 这防止了因锁定导致无法关闭取色器的问题
    if (window._colorPickerForceLocked) {
        // 添加超时覆盖，确保即使在强制锁定状态下也能关闭取色器
        if (Date.now() - window._colorPickerActivatedTime > 300) {
            window._colorPickerForceLocked = false;
        } else {
            return;
        }
    }

    // 如果取色器处于锁定状态，忽略离开事件
    if (colorPickerLocked) {
        return;
    }

    // 获取当前离开的元素
    const targetElement = event.currentTarget as Element;

    // 检查是否为central-preview-image类或其子元素
    const isCentralPreview = targetElement.classList.contains('central-preview-image') ||
        targetElement.closest('.central-preview-image') !== null;

    // 当离开的是中央预览图元素，且取色器处于激活状态时，立即关闭
    if (isCentralPreview && window._colorPickerActive) {
        // 立即关闭取色器，不添加额外判断
        resetColorPickerState();
    }
}

/**
 * 为预览图元素添加事件监听器的函数
 */
export function attachEventListeners(
    element: Element,
    onPreviewMouseEnterFn: () => Promise<void>,
    onPreviewMouseLeaveFn: (event: Event) => void
): void {
    // 检查元素是否已经添加过事件监听器，避免重复添加
    if ((element as HTMLElement).dataset.hasColorPickerEvents === 'true') {
        return;
    }

    // 移除可能存在的事件监听器，避免重复
    element.removeEventListener('mouseenter', onPreviewMouseEnterFn as EventListener);
    element.removeEventListener('mouseleave', onPreviewMouseLeaveFn as EventListener);

    // 添加新的事件监听器
    element.addEventListener('mouseenter', onPreviewMouseEnterFn as EventListener);
    element.addEventListener('mouseleave', onPreviewMouseLeaveFn as EventListener);

    // 标记元素已添加事件监听器
    (element as HTMLElement).dataset.hasColorPickerEvents = 'true';
}

/**
 * 为预览图元素添加mouseleave事件的增强处理，确保取色器能够正确关闭
 */
export function enhanceMouseLeaveDetection(element: Element): void {
    // 检查元素是否已经添加过增强mouseleave检测，避免重复添加
    if ((element as HTMLElement).dataset.hasEnhancedMouseLeave === 'true') {
        return;
    }

    // 增强的mouseleave事件处理函数
    const handleEnhancedMouseLeave = (event: Event) => {
        // 转换为MouseEvent类型
        const mouseEvent = event as MouseEvent;

        // 忽略取色器未激活的情况
        if (!window._colorPickerActive) {
            return;
        }

        // 强制解除锁定状态，确保可以关闭取色器
        window._colorPickerForceLocked = false;

        // 获取当前鼠标位置
        const mouseX = mouseEvent.clientX;
        const mouseY = mouseEvent.clientY;

        // 获取预览图元素
        const previewElement = window._colorPickerSourceElement;
        if (!previewElement) {
            return;
        }

        // 获取预览图区域
        const rect = previewElement.getBoundingClientRect();

        // 检查鼠标是否确实离开了预览图区域
        if (mouseX < rect.left || mouseX > rect.right ||
            mouseY < rect.top || mouseY > rect.bottom) {
            // 解除强制锁定并重置取色器状态
            resetColorPickerState();
        }
    };

    // 移除现有的mouseleave监听器，避免重复
    element.removeEventListener('mouseleave', handleEnhancedMouseLeave as EventListener);

    // 添加增强的mouseleave事件处理
    element.addEventListener('mouseleave', handleEnhancedMouseLeave as EventListener);

    // 标记元素已添加增强mouseleave检测
    (element as HTMLElement).dataset.hasEnhancedMouseLeave = 'true';
}

/**
 * 寻找并处理当前页面上所有的预览图元素
 */
export function findAndAttachToPreviewElements(
    onPreviewMouseEnterFn: () => Promise<void>,
    onPreviewMouseLeaveFn: (event: Event) => void
): boolean {
    try {
        // 直接查找中央预览图元素，不再查找所有可能的预览图元素
        const centralPreviewElement = document.querySelector('.central-preview-image');

        if (centralPreviewElement) {
            // 为元素添加事件监听
            attachEventListeners(centralPreviewElement, onPreviewMouseEnterFn, onPreviewMouseLeaveFn);
            // 添加增强的mouseleave检测
            enhanceMouseLeaveDetection(centralPreviewElement);

            return true;
        } else {
            return false;
        }
    } catch (error) {
        console.error('查找预览图元素时出错:', error);
        return false;
    }
}

/**
 * 初始化预览区域自动取色功能
 */
export function initializePreviewAreaAutoColorPicking(
    activateColorPickerFn: () => Promise<string | undefined>
): {
    observer: MutationObserver;
    cleanup: () => void;
} {
    // 记录状态，避免重复处理
    let isHandlingPreview = false;

    // 预览图鼠标进入处理函数
    const previewMouseEnterHandler = async () => {
        await onPreviewMouseEnter(activateColorPickerFn);
    };

    // 预览图鼠标离开处理函数
    const previewMouseLeaveHandler = (event: Event) => {
        onPreviewMouseLeave(event);
    };

    // 立即寻找并处理当前页面上的预览图元素
    findAndAttachToPreviewElements(previewMouseEnterHandler, previewMouseLeaveHandler);

    // 监听DOM变化，专门针对中央预览图元素
    const previewObserver = new MutationObserver((mutations) => {
        // 如果已经在处理中，不重复处理
        if (isHandlingPreview) return;

        let shouldScan = false;

        // 检查是否添加了中央预览图元素
        for (const mutation of mutations) {
            if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                for (const node of mutation.addedNodes) {
                    if (node instanceof Element) {
                        // 直接检查是否是中央预览图元素或包含中央预览图元素
                        if (node.classList?.contains('central-preview-image') ||
                            node.querySelector?.('.central-preview-image')) {
                            shouldScan = true;
                            break;
                        }
                    }
                }

                if (shouldScan) break;
            }
        }

        // 只有当检测到添加了中央预览图元素时才处理
        if (shouldScan) {
            isHandlingPreview = true;

            // 使用setTimeout让处理过程异步执行，避免阻塞主线程
            setTimeout(() => {
                findAndAttachToPreviewElements(previewMouseEnterHandler, previewMouseLeaveHandler);
                isHandlingPreview = false;
            }, 100);
        }
    });

    // 开始观察文档变化，观察整个body及其子元素
    previewObserver.observe(document.body, {
        childList: true,
        subtree: true
    });

    // 记录激活取色器的预览图元素
    const sourcePreviewElement = document.querySelector('.central-preview-image');
    if (sourcePreviewElement) {
        // 添加一个特殊标记，用于跟踪
        window._colorPickerSourceElement = sourcePreviewElement;

        // 给预览图元素添加一个自定义属性，便于后续识别
        (sourcePreviewElement as HTMLElement).dataset.isColorPickerSource = 'true';

        // 获取预览图元素的定位信息
        const rect = sourcePreviewElement.getBoundingClientRect();

        // 记录预览图的位置和尺寸
        window._colorPickerPreviewRect = {
            left: rect.left,
            top: rect.top,
            right: rect.right,
            bottom: rect.bottom,
            width: rect.width,
            height: rect.height
        };
    }

    // 返回观察器和清理函数
    const cleanup = () => {
        previewObserver.disconnect();
    };

    return {
        observer: previewObserver,
        cleanup
    };
}

/**
 * 初始化全局事件监听器
 */
export function initializeGlobalEventListeners(): {
    cleanup: () => void;
} {
    // 添加全局ESC键监听，确保在任何情况下都能关闭取色器
    document.addEventListener('keydown', globalEscapeKeyHandler);

    // 添加鼠标进入和离开窗口的事件监听器
    const mouseEnterHandler = () => {
        window._isMouseInWindow = true;
    };

    const mouseLeaveHandler = () => {
        window._isMouseInWindow = false;
    };

    document.addEventListener('mouseenter', mouseEnterHandler);
    document.addEventListener('mouseleave', mouseLeaveHandler);

    // 初始化时设置鼠标状态
    window._isMouseInWindow = document.hasFocus() &&
        window._lastMouseX > 0 && window._lastMouseX < window.innerWidth &&
        window._lastMouseY > 0 && window._lastMouseY < window.innerHeight;

    // 添加全局鼠标移动事件监听器来跟踪鼠标位置
    const globalMouseMoveHandler = (event: MouseEvent) => {
        window._lastMouseX = event.clientX;
        window._lastMouseY = event.clientY;

        // 更新鼠标是否在窗口内的状态
        const inWindow = event.clientX > 0 && event.clientX < window.innerWidth &&
            event.clientY > 0 && event.clientY < window.innerHeight;

        if (inWindow !== window._isMouseInWindow) {
            window._isMouseInWindow = inWindow;
        }
    };

    document.addEventListener('mousemove', globalMouseMoveHandler);

    // 添加窗口边界事件监听器
    const windowMouseLeaveHandler = (e: MouseEvent) => {
        if (!e.relatedTarget || !document.contains(e.relatedTarget as Node)) {
            window._isMouseInWindow = false;
            if (window._colorPickerActive) {
                resetColorPickerState();
            }
        }
    };

    const windowMouseEnterHandler = () => {
        window._isMouseInWindow = true;
    };

    // 添加窗口焦点事件监听器
    const windowBlurHandler = () => {
        if (window._colorPickerActive) {
            console.log('窗口失去焦点，自动关闭取色器');
            resetColorPickerState();
        }
    };

    const windowFocusHandler = () => {
        // 窗口获得焦点时的处理（如有需要）
    };

    document.addEventListener('mouseleave', windowMouseLeaveHandler, { capture: true });
    document.addEventListener('mouseenter', windowMouseEnterHandler, { capture: true });
    window.addEventListener('blur', windowBlurHandler);
    window.addEventListener('focus', windowFocusHandler);

    // 返回清理函数
    const cleanup = () => {
        document.removeEventListener('keydown', globalEscapeKeyHandler);
        document.removeEventListener('mouseenter', mouseEnterHandler);
        document.removeEventListener('mouseleave', mouseLeaveHandler);
        document.removeEventListener('mousemove', globalMouseMoveHandler);
        document.removeEventListener('mouseleave', windowMouseLeaveHandler);
        document.removeEventListener('mouseenter', windowMouseEnterHandler);
        window.removeEventListener('blur', windowBlurHandler);
        window.removeEventListener('focus', windowFocusHandler);
        resetColorPickerState();
    };

    return { cleanup };
}