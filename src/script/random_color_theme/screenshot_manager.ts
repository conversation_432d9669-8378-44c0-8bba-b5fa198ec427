/**
 * 截图管理模块
 * 负责截图的添加、删除、渲染和文件处理
 */

import { join } from '@tauri-apps/api/path';
import { mkdir, readDir, remove, exists, readFile } from "@tauri-apps/plugin-fs";
import { get_the_root_directory_path } from '../get_root_dir';
import { messageError, messageSuccess } from '../prompt_message';

// 截图保存路径
let screenshotPath: string | null = null;

// 当前选中的截图
let currentSelectedScreenshot: string | null = null;

// 添加删除操作锁定机制，防止并发删除
let isDeleting = false;

/**
 * 获取当前选中的截图
 */
export function getCurrentSelectedScreenshot(): string | null {
    return currentSelectedScreenshot;
}

/**
 * 设置当前选中的截图
 */
export function setCurrentSelectedScreenshot(screenshot: string | null): void {
    currentSelectedScreenshot = screenshot;
}

/**
 * 获取截图保存路径
 */
export function getPath(): string {
    if (!screenshotPath) {
        throw new Error('截图功能未初始化，请先调用init()');
    }
    return screenshotPath;
}

/**
 * 初始化截图功能
 */
export async function init(): Promise<void> {
    try {
        // 获取和创建截图目录
        const rootDir = await get_the_root_directory_path('random_color_theme');
        screenshotPath = await join(rootDir, 'screenshot');

        if (!await exists(screenshotPath)) {
            try {
                await mkdir(screenshotPath, { recursive: true });
            } catch (error) {
                console.error('创建截图目录失败:', error);
                // 即使创建目录失败，也继续初始化，因为目录可能已经存在
                // 或者会在后续操作中自动创建
            }
        }
    } catch (error) {
        console.error('初始化截图目录失败:', error);
        messageError(`初始化失败: ${error}`);
    }
}

/**
 * 更新截图选择状态和预览图
 */
export function updateScreenshotSelection(selectedItem: Element, imgSrc: string) {
    try {
        // 1. 批量移除所有激活状态，避免多次查询
        const activeItems = document.querySelectorAll('.screenshot-item.active');
        activeItems.forEach(item => item.classList.remove('active'));

        // 2. 标记当前项为激活状态
        selectedItem.classList.add('active');

        // 3. 创建或更新预览图
        createOrUpdatePreview(imgSrc);

        console.log('截图选择状态已更新');
    } catch (error) {
        console.error('更新截图选择状态失败:', error);
    }
}

/**
 * 创建或更新预览图
 */
async function createOrUpdatePreview(imgSrc: string): Promise<void> {
    try {
        // 缓存主容器，避免重复查询
        const mainContainer = document.querySelector('.color-search-container') || document.body;

        // 停用取色器（如果正在使用）
        try {
            const { resetColorPickerState } = await import('./color_picker');
            resetColorPickerState();
        } catch (error) {
            console.log('取色器模块未加载或停用失败:', error);
        }

        // 先移除所有已存在的预览图，避免重复点击造成叠加
        const existingPreviews = document.querySelectorAll('.central-preview-image');
        existingPreviews.forEach(preview => preview.remove());

        // 创建预览图容器
        const previewImageContainer = document.createElement('div');
        previewImageContainer.className = 'central-preview-image';

        // 创建预览图
        const previewImg = document.createElement('img');
        previewImg.src = imgSrc;
        previewImg.className = 'central-preview-img';

        // 优化图片加载，添加错误处理
        previewImg.onerror = () => {
            console.error('预览图加载失败:', imgSrc);
            previewImg.alt = '图片加载失败';
        };

        // 添加到容器
        previewImageContainer.appendChild(previewImg);

        // 添加预览图到主容器
        mainContainer.appendChild(previewImageContainer);

        console.log('预览图已创建/更新');
    } catch (error) {
        console.error('创建预览图失败:', error);
    }
}

/**
 * 添加一张截图
 * @returns 成功时返回文件路径，失败返回undefined
 */
export async function addScreenshot(): Promise<void> {
    try {
        // 确保初始化
        if (!screenshotPath) await init();

        // 获取提示元素
        const guideElement = document.querySelector('.add-screenshot-guide') as HTMLElement;

        // 调用截图功能
        const filePaths = await window.截屏(getPath());

        if (filePaths && filePaths.length > 0) {
            messageSuccess(`${filePaths.length}张截图已添加`);

            // 获取容器
            const container = document.getElementById('screenshots-container');
            if (!container) return;

            // 检查是否有"暂无截图"提示，并只在第一次添加时处理
            const noScreenshotsElement = container.querySelector('.no-screenshots');
            if (noScreenshotsElement) {
                container.innerHTML = '';
            }

            // 隐藏添加截图提示
            if (guideElement) {
                guideElement.style.display = 'none';
            }

            for (const filePath of filePaths) {
                let fileName = '';
                try {
                    // 提取文件名（移除路径部分）
                    fileName = filePath.split(/[\/\\]/).pop() || '';

                    // 计算新添加的截图序号
                    const screenshotElements = container.querySelectorAll('.screenshot-item');
                    const screenshotNumber = screenshotElements.length + 1;

                    // 创建新的截图元素并添加到容器
                    const imgContainer = createScreenshotElement(fileName, screenshotNumber);

                    // 直接读取新添加的图片
                    const imageData = await readImageAsDataURL(filePath);

                    if (imageData) {
                        setupScreenshotElement(imgContainer, fileName, imageData);
                    }

                    // 将新截图添加到容器末尾
                    container.appendChild(imgContainer);

                    // 滚动到新添加的截图位置
                    setTimeout(() => {
                        imgContainer.scrollIntoView({ behavior: 'smooth', block: 'nearest', inline: 'end' });
                    }, 100);
                } catch (error) {
                    console.error(`渲染新添加的截图 ${fileName || filePath} 失败:`, error);
                }
            }
        }
    } catch (error) {
        console.error('截图失败:', error);
        messageError(`截图失败: ${error}`);
    }
}

/**
 * 创建截图元素
 */
function createScreenshotElement(fileName: string, screenshotNumber: number): HTMLElement {
    const imgContainer = document.createElement('div');
    imgContainer.className = 'screenshot-item';
    imgContainer.style.opacity = '0';
    imgContainer.style.transform = 'scale(0.95)';
    imgContainer.style.transition = 'opacity 0.3s ease, transform 0.3s ease';

    // 添加数据属性
    imgContainer.dataset.index = screenshotNumber.toString();
    imgContainer.dataset.filename = fileName;
    imgContainer.dataset.loaded = 'loading';

    // 创建图片容器
    const imgWrapper = document.createElement('div');
    imgWrapper.className = 'screenshot-img-container';

    // 创建图片元素
    const img = document.createElement('img');
    img.alt = fileName;
    img.loading = 'eager'; // 立即加载，不使用懒加载
    img.className = 'screenshot-img';

    // 将图片添加到图片容器
    imgWrapper.appendChild(img);

    // 创建底部信息容器
    const footer = document.createElement('div');
    footer.className = 'screenshot-footer';

    // 添加序号
    const number = document.createElement('span');
    number.className = 'screenshot-number';
    number.textContent = `${screenshotNumber}`;

    // 添加删除按钮
    const deleteBtn = document.createElement('button');
    deleteBtn.className = 'screenshot-delete-btn';
    deleteBtn.textContent = '删除';
    deleteBtn.addEventListener('click', async (e) => {
        e.stopPropagation(); // 阻止事件冒泡
        await deleteScreenshot(fileName);
    });

    // 将元素添加到底部信息容器
    footer.appendChild(number);
    footer.appendChild(deleteBtn);

    // 将元素添加到容器
    imgContainer.appendChild(imgWrapper);
    imgContainer.appendChild(footer);

    return imgContainer;
}

/**
 * 设置截图元素的图片和事件
 */
function setupScreenshotElement(imgContainer: HTMLElement, fileName: string, imageData: string): void {
    const img = imgContainer.querySelector('.screenshot-img') as HTMLImageElement;

    // 图片加载完成后显示容器并自动选中
    img.onload = () => {
        // 立即显示
        imgContainer.style.opacity = '1';
        imgContainer.style.transform = 'scale(1)';
        imgContainer.dataset.loaded = 'loaded';

        // 更新当前选中的截图
        currentSelectedScreenshot = fileName;

        // 更新UI状态
        updateScreenshotSelection(imgContainer, img.src);
    };

    // 直接设置图片源，无需使用占位图
    img.src = imageData;
}

/**
 * 删除指定截图
 * @param fileName 文件名
 * @returns 是否删除成功
 */
export async function deleteScreenshot(fileName: string): Promise<boolean> {
    // 防止并发删除操作
    if (isDeleting) {
        console.warn('删除操作正在进行中，请稍后再试');
        return false;
    }

    isDeleting = true;

    try {
        const path = getPath();
        // 修复路径重复问题，确保文件名不包含完整路径
        const fileNameOnly = fileName.split(/[\/\\]/).pop() || fileName;
        const filePath = await join(path, fileNameOnly);

        // 检查文件是否存在
        if (!await exists(filePath)) {
            console.warn(`文件不存在: ${filePath}`);
            return false;
        }

        // 获取截图元素
        const container = document.getElementById('screenshots-container');
        // 使用文件名而不是路径来查找元素
        const screenshotElement = container?.querySelector(`.screenshot-item[data-filename="${fileNameOnly}"]`) as HTMLElement;

        // 检查是否需要清除当前选中状态
        const isCurrentSelected = currentSelectedScreenshot === fileNameOnly;

        // 执行删除操作
        return await performDeleteOperation(
            filePath, 
            fileNameOnly, 
            screenshotElement, 
            isCurrentSelected, 
            container
        );

    } catch (error) {
        console.error(`删除截图失败:`, error);
        messageError(`删除失败: ${error}`);
        return false;
    } finally {
        // 确保总是释放锁定
        isDeleting = false;
    }
}

/**
 * 执行删除操作
 */
async function performDeleteOperation(
    filePath: string,
    fileNameOnly: string,
    screenshotElement: HTMLElement | null,
    isCurrentSelected: boolean,
    container: Element | null
): Promise<boolean> {
    // 添加渐隐动画
    if (screenshotElement) {
        screenshotElement.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
        screenshotElement.style.opacity = '0';
        screenshotElement.style.transform = 'scale(0.9)';

        // 等待动画完成后从DOM中移除
        await new Promise<void>((resolve) => {
            setTimeout(async () => {
                try {
                    await deleteFileAndCleanup(filePath, fileNameOnly);
                    screenshotElement.remove();
                    await handlePostDeleteUI(container, isCurrentSelected);
                    messageSuccess('截图已删除');
                    resolve();
                } catch (error) {
                    console.error(`删除文件失败: ${error}`);
                    messageError(`删除文件失败: ${error}`);
                    resolve();
                }
            }, 300);
        });

        return true;
    } else {
        // 如果没有找到对应的DOM元素，回退到完全重新渲染
        try {
            await deleteFileAndCleanup(filePath, fileNameOnly);

            // 如果删除的是当前选中的截图，清除选中状态
            if (isCurrentSelected) {
                currentSelectedScreenshot = null;
            }

            await renderScreenshots(); // 删除后重新渲染
            messageSuccess('截图已删除');
            return true;
        } catch (error) {
            console.error(`删除文件失败: ${error}`);
            messageError(`删除文件失败: ${error}`);
            return false;
        }
    }
}

/**
 * 删除文件和相关清理
 */
async function deleteFileAndCleanup(filePath: string, fileNameOnly: string): Promise<void> {
    // 删除图片文件
    await remove(filePath);

    // 删除可能存在的关联JSON文件 - 删除失败不阻塞进程
    try {
        const path = getPath();
        const jsonPath = await join(path, `${fileNameOnly.replace(/\.\w+$/i, '')}.json`);
        if (await exists(jsonPath)) {
            await remove(jsonPath);
        }
    } catch (jsonError) {
        console.error(`删除关联JSON文件失败，但不影响主要流程: ${jsonError}`);
    }
}

/**
 * 处理删除后的UI更新
 */
async function handlePostDeleteUI(container: Element | null, isCurrentSelected: boolean): Promise<void> {
    // 检查是否还有截图
    const remainingScreenshots = container?.querySelectorAll('.screenshot-item');

    if (!remainingScreenshots || remainingScreenshots.length === 0) {
        // 如果没有截图，显示"暂无截图"提示
        if (container) {
            container.innerHTML = '<div class="no-screenshots">暂无截图</div>';

            // 显示添加截图提示
            const guideElement = document.querySelector('.add-screenshot-guide') as HTMLElement;
            if (guideElement) {
                guideElement.style.display = 'flex';
            }

            // 清除当前选中的截图
            currentSelectedScreenshot = null;

            // 停用取色器（如果正在使用）
            try {
                const { resetColorPickerState } = await import('./color_picker');
                resetColorPickerState();
            } catch (error) {
                console.log('取色器模块未加载或停用失败:', error);
            }
        }
    } else {
        // 重新排序序号
        remainingScreenshots.forEach((item, index) => {
            const numberElement = item.querySelector('.screenshot-number');
            if (numberElement) {
                numberElement.textContent = `${index + 1}`;
            }
            // 更新索引数据属性
            (item as HTMLElement).dataset.index = index.toString();
        });

        // 如果删除的是当前选中的截图，自动选中第一个可用的截图
        if (isCurrentSelected) {
            await selectFirstAvailableScreenshot(remainingScreenshots);
        }
    }
}

/**
 * 选择第一个可用的截图
 */
async function selectFirstAvailableScreenshot(remainingScreenshots: NodeListOf<Element>): Promise<void> {
    // 停用取色器（因为要切换截图）
    try {
        const { resetColorPickerState } = await import('./color_picker');
        resetColorPickerState();
    } catch (error) {
        console.log('取色器模块未加载或停用失败:', error);
    }

    const firstRemaining = remainingScreenshots[0] as HTMLElement;
    const firstImg = firstRemaining.querySelector('.screenshot-img') as HTMLImageElement;
    const filename = firstRemaining.dataset.filename;

    if (filename && firstImg && firstImg.src && !firstImg.src.includes('data:image/svg+xml')) {
        currentSelectedScreenshot = filename;
        updateScreenshotSelection(firstRemaining, firstImg.src);
    } else {
        currentSelectedScreenshot = null;
    }
}

/**
 * 清空所有截图
 * @returns 是否全部清除成功
 */
export async function clearAllScreenshots(): Promise<boolean> {
    try {
        const path = getPath();

        // 获取所有截图文件
        const files = await readDir(path);
        const imageFiles = files.filter(file =>
            file.name && /\.(png|jpe?g)$/i.test(file.name)
        );

        if (imageFiles.length === 0) {
            return true;
        }

        // 获取容器元素
        const container = document.getElementById('screenshots-container');
        const screenshotElements = container?.querySelectorAll('.screenshot-item');

        // 清除当前选中状态
        currentSelectedScreenshot = null;

        // 停用取色器（如果正在使用）
        try {
            const { resetColorPickerState } = await import('./color_picker');
            resetColorPickerState();
        } catch (error) {
            console.log('取色器模块未加载或停用失败:', error);
        }

        // 执行批量删除
        return await performBatchDelete(imageFiles, screenshotElements, container, path);

    } catch (error) {
        console.error('清除所有截图失败:', error);
        messageError(`清除失败: ${error}`);
        return false;
    }
}

/**
 * 执行批量删除操作
 */
async function performBatchDelete(
    imageFiles: any[],
    screenshotElements: NodeListOf<Element> | undefined,
    container: Element | null,
    path: string
): Promise<boolean> {
    // 添加渐隐动画到所有截图
    if (screenshotElements && screenshotElements.length > 0) {
        screenshotElements.forEach((element) => {
            (element as HTMLElement).style.transition = 'opacity 0.3s ease, transform 0.3s ease';
            (element as HTMLElement).style.opacity = '0';
            (element as HTMLElement).style.transform = 'scale(0.9)';
        });

        // 等待动画完成后执行删除操作
        setTimeout(async () => {
            const allSuccess = await deleteAllFiles(imageFiles, path);
            cleanupAfterBatchDelete(container);
            if (allSuccess) {
                messageSuccess('所有截图已清除');
            }
        }, 300);

        return true;
    } else {
        // 回退到原始方法
        const allSuccess = await deleteAllFiles(imageFiles, path);
        cleanupAfterBatchDelete(container);
        if (allSuccess) {
            messageSuccess('所有截图已清除');
        }
        return allSuccess;
    }
}

/**
 * 删除所有文件
 */
async function deleteAllFiles(imageFiles: any[], path: string): Promise<boolean> {
    let allSuccess = true;
    for (const file of imageFiles) {
        if (file.name) {
            try {
                const filePath = await join(path, file.name);
                await remove(filePath);

                // 删除可能存在的关联JSON文件 - 删除失败不阻塞进程
                try {
                    const jsonPath = await join(path, `${file.name.replace(/\.\w+$/i, '')}.json`);
                    if (await exists(jsonPath)) {
                        await remove(jsonPath);
                    }
                } catch (jsonError) {
                    console.error(`删除关联JSON文件失败，但不影响主要流程: ${jsonError}`);
                }
            } catch (e) {
                console.error(`删除文件 ${file.name} 失败:`, e);
                allSuccess = false;
            }
        }
    }
    return allSuccess;
}

/**
 * 批量删除后的清理工作
 */
function cleanupAfterBatchDelete(container: Element | null): void {
    // 清空容器并显示"暂无截图"提示
    if (container) {
        container.innerHTML = '<div class="no-screenshots">暂无截图</div>';

        // 显示添加截图提示
        const guideElement = document.querySelector('.add-screenshot-guide') as HTMLElement;
        if (guideElement) {
            guideElement.style.display = 'flex';
        }
    }

    // 清除预览区域
    const existingPreview = document.querySelector('.central-preview-image');
    if (existingPreview) {
        existingPreview.remove();
    }
}

/**
 * 读取并排序截图文件
 */
async function readAndSortScreenshots() {
    try {
        // 获取目录路径
        const path = getPath();

        // 读取目录下所有文件
        const files = await readDir(path);

        // 过滤出图片文件并按时间戳排序
        const imageFiles = files
            .filter(file => file.name && /\.(png|jpe?g)$/i.test(file.name))
            .sort((a, b) => {
                // 从文件名中提取时间戳
                const getTimestamp = (filename: string) => {
                    const match = filename.match(/(\d+)\.(png|jpe?g)$/i);
                    return match ? parseInt(match[1]) : 0;
                };

                const timeA = getTimestamp(a.name || '');
                const timeB = getTimestamp(b.name || '');

                return timeA - timeB;
            });

        return imageFiles;

    } catch (error) {
        console.error('读取并排序截图文件失败:', error);
        return [];
    }
}

/**
 * 读取图片文件并转换为Data URL
 * 直接读取图片文件的二进制数据，不进行压缩处理，提高加载速度
 * @param filePath 图片文件路径
 * @returns 返回Data URL字符串
 */
async function readImageAsDataURL(filePath: string): Promise<string | null> {
    try {
        // 提取文件名，避免路径问题
        const fileName = filePath.split(/[\/\\]/).pop();
        if (!fileName) {
            console.error('无效的文件路径:', filePath);
            return null;
        }

        // 确保使用正确的路径
        const fullPath = await join(getPath(), fileName);

        // 检查文件是否存在
        if (!await exists(fullPath)) {
            console.error(`文件不存在: ${fullPath}`);
            return null;
        }

        // 读取图片文件的二进制数据
        const data = await readFile(fullPath);

        // 检查数据大小，避免加载过大的文件
        if (data.length > 10 * 1024 * 1024) { // 10MB限制
            console.warn(`文件过大，跳过加载: ${fullPath} (${(data.length / 1024 / 1024).toFixed(2)}MB)`);
            return null;
        }

        // 检测文件类型并确定MIME类型
        let mimeType = 'image/png'; // 默认MIME类型

        // 通过文件扩展名判断MIME类型
        const lowerPath = fullPath.toLowerCase();
        if (lowerPath.endsWith('.jpg') || lowerPath.endsWith('.jpeg')) {
            mimeType = 'image/jpeg';
        } else if (lowerPath.endsWith('.png')) {
            mimeType = 'image/png';
        }

        // 将二进制数据转换为base64编码的Data URL
        // 使用分片处理大数据，避免字符串拼接导致的内存问题
        const chunkSize = 32 * 1024; // 32KB 分片
        const chunks: string[] = [];
        const uint8Array = new Uint8Array(data);

        for (let i = 0; i < uint8Array.length; i += chunkSize) {
            const chunk = uint8Array.slice(i, i + chunkSize);
            chunks.push(String.fromCharCode(...chunk));
        }

        const base64Data = btoa(chunks.join(''));

        return `data:${mimeType};base64,${base64Data}`;
    } catch (error) {
        console.error('读取图片文件失败:', error);
        return null;
    }
}

/**
 * 渲染截图到容器中，优化性能
 */
export async function renderScreenshots(): Promise<void> {
    try {
        const container = document.getElementById('screenshots-container');
        // 获取提示元素
        const guideElement = document.querySelector('.add-screenshot-guide') as HTMLElement;

        if (!container) {
            console.warn('未找到截图容器元素');
            return;
        }

        // 清空容器
        container.innerHTML = '';

        // 获取排序后的截图文件
        const imageFiles = await readAndSortScreenshots();

        // 根据截图数量显示或隐藏提示
        if (imageFiles.length === 0) {
            container.innerHTML = '<div class="no-screenshots">暂无截图</div>';
            // 显示提示元素
            if (guideElement) {
                guideElement.style.display = 'flex';
            }
            return;
        } else {
            // 隐藏提示元素
            if (guideElement) {
                guideElement.style.display = 'none';
            }
        }

        // 批量渲染截图
        await renderScreenshotsBatch(imageFiles, container);

    } catch (error) {
        console.error('渲染截图失败:', error);
        messageError(`渲染截图失败: ${error}`);
    }
}

/**
 * 批量渲染截图
 */
async function renderScreenshotsBatch(imageFiles: any[], container: HTMLElement): Promise<void> {
    // 创建批量加载器，每批处理5张图片
    const batchSize = 5;
    const batches = Math.ceil(imageFiles.length / batchSize);

    // 跟踪第一个完全加载的截图
    let firstLoadedItem: HTMLElement | null = null;

    // 预先创建所有截图容器元素
    for (let i = 0; i < imageFiles.length; i++) {
        const file = imageFiles[i];
        if (!file.name) continue;

        try {
            const imgContainer = await createBatchScreenshotElement(file, i, batchSize);
            container.appendChild(imgContainer);

            // 检查是否是当前选中的截图
            const fileName = file.name.split(/[\/\\]/).pop() || file.name;
            if (currentSelectedScreenshot === fileName) {
                imgContainer.classList.add('active');
            }

            // 渐变显示容器
            setTimeout(() => {
                imgContainer.style.opacity = '1';
                imgContainer.style.transform = 'scale(1)';
            }, 10 + i * 20); // 添加小的延迟，创建交错效果
        } catch (error) {
            console.error(`创建截图容器 ${file.name} 失败:`, error);
        }
    }

    // 批量加载图片
    await loadScreenshotsBatch(batches, firstLoadedItem);
}

/**
 * 创建批量截图元素
 */
async function createBatchScreenshotElement(file: any, index: number, batchSize: number): Promise<HTMLElement> {
    // 从完整路径中提取文件名
    const fileName = file.name.split(/[\/\\]/).pop() || file.name;

    // 创建截图容器
    const imgContainer = document.createElement('div');
    imgContainer.className = 'screenshot-item';
    // 添加初始透明度和缩放效果
    imgContainer.style.opacity = '0';
    imgContainer.style.transform = 'scale(0.95)';
    imgContainer.style.transition = 'opacity 0.3s ease, transform 0.3s ease';

    // 添加数据属性，用于稍后加载图片
    imgContainer.dataset.index = index.toString();
    imgContainer.dataset.filename = fileName;
    imgContainer.dataset.batchIndex = Math.floor(index / batchSize).toString();

    // 创建图片容器
    const imgWrapper = document.createElement('div');
    imgWrapper.className = 'screenshot-img-container';

    // 创建更美观的占位图片元素
    const img = document.createElement('img');
    img.alt = fileName;
    img.loading = 'lazy'; // 启用浏览器的原生懒加载

    // 改进占位图，使用更美观的渐变效果
    img.src = 'data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" width="80" height="180" viewBox="0 0 80 180"%3E%3Cdefs%3E%3ClinearGradient id="grad" x1="0%25" y1="0%25" x2="100%25" y2="100%25"%3E%3Cstop offset="0%25" style="stop-color:%23f5f5f5;stop-opacity:1" /%3E%3Cstop offset="100%25" style="stop-color:%23e0e0e0;stop-opacity:1" /%3E%3C/linearGradient%3E%3C/defs%3E%3Crect width="80" height="180" fill="url(%23grad)" /%3E%3Ctext x="40" y="90" font-family="Arial" font-size="10" text-anchor="middle" fill="%23999"%3E加载中...%3C/text%3E%3C/svg%3E';
    img.className = 'screenshot-img';

    // 将图片添加到图片容器
    imgWrapper.appendChild(img);

    // 创建底部信息容器
    const footer = document.createElement('div');
    footer.className = 'screenshot-footer';

    // 添加序号
    const number = document.createElement('span');
    number.className = 'screenshot-number';
    number.textContent = `${index + 1}`;

    // 添加删除按钮
    const deleteBtn = document.createElement('button');
    deleteBtn.className = 'screenshot-delete-btn';
    deleteBtn.textContent = '删除';
    deleteBtn.addEventListener('click', async (e) => {
        e.stopPropagation(); // 阻止事件冒泡
        await deleteScreenshot(fileName);
    });

    // 将元素添加到底部信息容器
    footer.appendChild(number);
    footer.appendChild(deleteBtn);

    // 将元素添加到容器
    imgContainer.appendChild(imgWrapper);
    imgContainer.appendChild(footer);

    return imgContainer;
}

/**
 * 批量加载图片
 */
async function loadScreenshotsBatch(batches: number, firstLoadedItem: HTMLElement | null): Promise<void> {
    for (let batchIndex = 0; batchIndex < batches; batchIndex++) {
        // 延迟加载每一批，避免一次性加载所有图片
        setTimeout(async () => {
            // 找到属于当前批次的所有容器
            const batchContainers = Array.from(document.querySelectorAll(`.screenshot-item[data-batch-index="${batchIndex}"]`)) as HTMLElement[];

            // 并行加载当前批次的所有图片
            await Promise.all(batchContainers.map(async (container) => {
                const filename = container.dataset.filename;
                if (!filename || container.dataset.loaded === 'loaded') return;

                try {
                    // 标记为正在加载
                    container.dataset.loaded = 'loading';

                    // 获取完整文件路径
                    const fullPath = await join(getPath(), filename);

                    // 直接读取图片数据，不经过压缩处理
                    const imageData = await readImageAsDataURL(fullPath);

                    if (imageData) {
                        const img = container.querySelector('img');
                        if (img) {
                            img.onload = () => {
                                container.dataset.loaded = 'loaded';

                                // 如果是第一个加载完成的图片并且没有当前选中的截图，自动选中它
                                if (!firstLoadedItem) {
                                    firstLoadedItem = container;

                                    // 如果没有当前选中的截图，设置当前图片为选中状态
                                    if (!currentSelectedScreenshot) {
                                        currentSelectedScreenshot = filename;
                                        updateScreenshotSelection(container, img.src);
                                    }
                                }

                                // 如果这是当前选中的截图，显示预览图
                                if (currentSelectedScreenshot === filename) {
                                    updateScreenshotSelection(container, img.src);
                                }
                            };
                            img.src = imageData;
                        }
                    }
                } catch (error) {
                    console.error(`加载图片 ${filename} 失败:`, error);
                    container.dataset.loaded = 'error';
                }
            }));

            // 如果是最后一批且没有找到任何选中的图片，选择第一个
            if (batchIndex === batches - 1) {
                setTimeout(() => {
                    if (!document.querySelector('.screenshot-item.active') && firstLoadedItem) {
                        const img = firstLoadedItem.querySelector('img') as HTMLImageElement;
                        if (img && img.src && !img.src.includes('data:image/svg+xml')) {
                            const filename = firstLoadedItem.dataset.filename;
                            if (filename) {
                                currentSelectedScreenshot = filename;
                                updateScreenshotSelection(firstLoadedItem, img.src);
                            }
                        }
                    }
                }, 300);
            }
        }, batchIndex * 100); // 每100ms加载一批
    }
}

/**
 * 更新截图显示区域和预览区域
 * 在主题生成/应用完成后调用，确保UI与实际文件状态同步
 */
export async function updateScreenshotAndPreviewArea(): Promise<void> {
    try {
        console.log('正在更新截图显示区域...');
        await renderScreenshots();

        // 如果当前选中的截图已被删除，清除预览区域
        if (currentSelectedScreenshot) {
            const screenshotPath = await join(getPath(), currentSelectedScreenshot);
            if (!await exists(screenshotPath)) {
                console.log('当前选中的截图已被删除，清除预览区域');
                currentSelectedScreenshot = null;
                const existingPreview = document.querySelector('.central-preview-image');
                if (existingPreview) {
                    existingPreview.remove();
                }
            }
        }

        console.log('截图显示区域更新完成');
    } catch (error) {
        console.error('更新截图显示区域失败:', error);
    }
}