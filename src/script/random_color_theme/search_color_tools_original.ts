import { join } from '@tauri-apps/api/path';
import { mkdir, readDir, remove, exists } from "@tauri-apps/plugin-fs";
import { get_the_root_directory_path } from '../get_root_dir';
import { messageError, messageSuccess, messageLoading } from '../prompt_message';
import { random_color_theme } from './random_color_theme';
import {
    applyColorValueTheme,
    applyImageTheme,
    applyFullTheme,
    getAvailableModules,
    getModuleDisplayName
} from './apply_random_theme';
import { invoke } from '@tauri-apps/api/core';
import { writeImage } from '@tauri-apps/plugin-clipboard-manager';
import { readFile } from '@tauri-apps/plugin-fs';


// 定义全局类型
declare global {
    interface Window {
        截屏: (outpath?: string | undefined) => Promise<string[] | undefined>;
        reloadDataJsonAndRefresh?: () => Promise<void>;
        __TAURI__?: any;
        isPrivilegedUser?: boolean;
    }
}

// 可选的TAURI声明，使用接口合并避免与现有声明冲突
// @ts-ignore - Window接口扩展，用于类型声明
declare interface Window {
    __TAURI__?: any;
    isPrivilegedUser?: boolean;
}

// 截图保存路径
let screenshotPath: string | null = null;

// 当前选中的截图
let currentSelectedScreenshot: string | null = null;

// 添加删除操作锁定机制，防止并发删除
let isDeleting = false;

// 全局变量：保存模块状态的修改（键值对形式）
let moduleStatusModifications: { [ key: string ]: boolean } = {};
let hasModifications = false;

/**
 * 获取用户名
 */
async function getUserName(): Promise<string> {
    try {
        const { readTextFile } = await import('@tauri-apps/plugin-fs');
        const cachePath = await join(await get_the_root_directory_path('cache'), 'cache.json');

        if (await exists(cachePath)) {
            const cacheContent = await readTextFile(cachePath);
            const cache = JSON.parse(cacheContent);
            return cache.username || '';
        }
        return '';
    } catch (error) {
        console.error('获取用户名失败:', error);
        return '';
    }
}

/**
 * 检查是否为特权用户
 */
async function checkPrivilegedUser(): Promise<boolean> {
    try {
        const username = await getUserName();
        console.log('当前用户名:', username);
        // 暂时直接返回true以便测试特权用户功能
        return true; // username === 'zhangchuanqiang';
    } catch (error) {
        console.error('检查特权用户失败:', error);
        return true; // 错误时也返回true以便测试
    }
}

/**
 * 创建生成主题按钮（带模块选择）
 * @returns 创建的按钮元素
 */
function createGenerateThemeButton(): HTMLElement {
    const container = document.createElement('div');
    container.className = 'apply-theme-button-group';

    // 主按钮
    const mainButton = document.createElement('button');
    mainButton.className = 'add-screenshot-button apply-theme-main-btn';
    mainButton.textContent = '生成随机颜色主题';

    // 下拉按钮
    const dropdownButton = document.createElement('button');
    dropdownButton.className = 'add-screenshot-button apply-theme-dropdown-btn';
    dropdownButton.textContent = '▼';
    dropdownButton.style.padding = '8px 2px';
    dropdownButton.style.marginLeft = '-5px';

    // 下拉菜单
    const dropdownMenu = document.createElement('div');
    dropdownMenu.className = 'apply-theme-dropdown-menu';
    dropdownMenu.style.display = 'none';

    container.appendChild(mainButton);
    container.appendChild(dropdownButton);
    container.appendChild(dropdownMenu);

    // 主按钮点击事件 - 打开模块选择弹窗
    mainButton.addEventListener('click', async () => {
        // 直接触发下拉按钮的点击事件，打开模块选择菜单
        dropdownButton.click();
    });

    // 下拉按钮点击事件 - 显示/隐藏模块选择
    dropdownButton.addEventListener('click', async () => {
        const isVisible = dropdownMenu.style.display !== 'none';

        if (isVisible) {
            dropdownMenu.style.display = 'none';
            dropdownButton.textContent = '▼';
            dropdownButton.classList.remove('active');
        } else {
            // 获取可用模块并生成选项
            await populateGenerateModuleOptions(dropdownMenu);
            dropdownMenu.style.display = 'block';
            dropdownButton.textContent = '▲';
            dropdownButton.classList.add('active');
        }
    });

    // 点击其他地方时隐藏下拉菜单
    document.addEventListener('click', (e) => {
        if (!container.contains(e.target as Node)) {
            dropdownMenu.style.display = 'none';
            dropdownButton.textContent = '▼';
            dropdownButton.classList.remove('active');
        }
    });

    return container;
}

/**
 * 创建应用主题按钮
 * @param buttonText 按钮文本
 * @param versionType 版本类型
 * @returns 创建的按钮元素
 */
function createApplyThemeButton(buttonText: string, versionType: 'color' | 'image' | 'full'): HTMLElement {
    const container = document.createElement('div');
    container.className = 'apply-theme-button-group';

    // 主按钮
    const mainButton = document.createElement('button');
    mainButton.className = 'add-screenshot-button apply-theme-main-btn';
    mainButton.textContent = `应用${buttonText}`;

    // 下拉按钮
    const dropdownButton = document.createElement('button');
    dropdownButton.className = 'add-screenshot-button apply-theme-dropdown-btn';
    dropdownButton.textContent = '▼';
    dropdownButton.style.padding = '8px 6px';
    dropdownButton.style.marginLeft = '2px';

    // 下拉菜单
    const dropdownMenu = document.createElement('div');
    dropdownMenu.className = 'apply-theme-dropdown-menu';
    dropdownMenu.style.display = 'none';

    container.appendChild(mainButton);
    container.appendChild(dropdownButton);
    container.appendChild(dropdownMenu);

    // 主按钮点击事件 - 打开下拉菜单
    mainButton.addEventListener('click', async (e) => {
        e.preventDefault();
        e.stopPropagation();

        // 检查当前菜单是否可见
        const isVisible = dropdownMenu.style.display !== 'none';

        if (isVisible) {
            // 隐藏菜单
            dropdownMenu.style.display = 'none';
            dropdownButton.textContent = '▼';
            dropdownButton.classList.remove('active');
        } else {
            // 关闭所有其他已打开的下拉菜单
            const allDropdownMenus = document.querySelectorAll('.apply-theme-dropdown-menu');
            const allDropdownButtons = document.querySelectorAll('.apply-theme-dropdown-btn');

            allDropdownMenus.forEach(menu => {
                if (menu !== dropdownMenu) {
                    (menu as HTMLElement).style.display = 'none';
                }
            });

            allDropdownButtons.forEach(btn => {
                if (btn !== dropdownButton) {
                    (btn as HTMLElement).textContent = '▼';
                    (btn as HTMLElement).classList.remove('active');
                }
            });

            // 显示当前菜单
            await populateModuleOptions(dropdownMenu, versionType);
            dropdownMenu.style.display = 'block';
            dropdownButton.textContent = '▲';
            dropdownButton.classList.add('active');
        }
    });

    // 下拉按钮点击事件 - 显示/隐藏模块选择
    dropdownButton.addEventListener('click', async (e) => {
        e.preventDefault();
        e.stopPropagation();

        const isVisible = dropdownMenu.style.display !== 'none';

        if (isVisible) {
            dropdownMenu.style.display = 'none';
            dropdownButton.textContent = '▼';
            dropdownButton.classList.remove('active');
        } else {
            // 关闭所有其他已打开的下拉菜单
            const allDropdownMenus = document.querySelectorAll('.apply-theme-dropdown-menu');
            const allDropdownButtons = document.querySelectorAll('.apply-theme-dropdown-btn');

            allDropdownMenus.forEach(menu => {
                if (menu !== dropdownMenu) {
                    (menu as HTMLElement).style.display = 'none';
                }
            });

            allDropdownButtons.forEach(btn => {
                if (btn !== dropdownButton) {
                    (btn as HTMLElement).textContent = '▼';
                    (btn as HTMLElement).classList.remove('active');
                }
            });

            // 获取可用模块并生成选项
            await populateModuleOptions(dropdownMenu, versionType);
            dropdownMenu.style.display = 'block';
            dropdownButton.textContent = '▲';
            dropdownButton.classList.add('active');
        }
    });

    // 点击其他地方时隐藏下拉菜单
    document.addEventListener('click', (e) => {
        if (!container.contains(e.target as Node)) {
            dropdownMenu.style.display = 'none';
            dropdownButton.textContent = '▼';
            dropdownButton.classList.remove('active');
        }
    });

    return container;
}

/**
 * 加载配置文件中的模块状态
 */
async function loadModuleStatusFromConfig(): Promise<void> {
    try {
        // 使用正确的配置文件路径 - 通过Tauri命令获取
        const { invoke } = await import('@tauri-apps/api/core');
        const configPath = await invoke('获取配置文件路径', { fileName: 'random_color_theme_config.json5' }) as string;
        console.log('配置文件路径:', configPath);

        if (await exists(configPath)) {
            const { readTextFile } = await import('@tauri-apps/plugin-fs');
            const configContent = await readTextFile(configPath);
            console.log('配置文件内容预览:', configContent.substring(0, 200) + '...');

            const config = JSON.parse(configContent.replace(/\/\*[\s\S]*?\*\/|\/\/.*$/gm, ''));
            console.log('解析后的配置对象:', config);
            console.log('配置中的module字段:', config.module);
            console.log('module字段类型:', typeof config.module);
            console.log('是否为数组:', Array.isArray(config.module));

            // 优先读取配置文件中的module对象
            if (config.module && typeof config.module === 'object' && !Array.isArray(config.module)) {
                // 直接使用配置文件中的module对象，包含所有键值对（true和false）
                moduleStatusModifications = { ...config.module };
                console.log('✅ 从配置文件加载模块状态 (对象格式):', moduleStatusModifications);
                console.log('✅ 模块数量:', Object.keys(moduleStatusModifications).length);
                console.log('✅ 模块列表:', Object.keys(moduleStatusModifications));
            } else if (Array.isArray(config.module)) {
                // 如果配置文件中是数组格式，转换为对象格式（兼容旧版本）
                const moduleObj: { [ key: string ]: boolean } = {};
                config.module.forEach((moduleName: string) => {
                    moduleObj[ moduleName ] = true; // 数组中的模块默认为启用
                });

                moduleStatusModifications = { ...moduleObj };
                console.log('✅ 从配置文件数组格式转换模块状态:', moduleStatusModifications);
                console.log('✅ 模块数量:', Object.keys(moduleStatusModifications).length);
            } else {
                console.error('❌ 配置文件中没有找到有效的module配置');
                throw new Error('配置文件中没有找到有效的module配置');
            }
        } else {
            console.error('❌ 配置文件不存在:', configPath);
            throw new Error('配置文件不存在');
        }
    } catch (error) {
        console.error('❌ 加载模块状态配置失败:', error);
        console.log('🔄 使用默认模块配置');

        // 使用默认配置（包含常见的模块）
        const defaultModuleStatus: { [ key: string ]: boolean } = {
            "com.android.settings": true,
            "com.android.contacts": true,
            "com.android.mms": true,
            "com.android.systemui": true,
            "miui.systemui.plugin": true,
            "com.miui.notification": true,
            "com.miui.securitycenter": true,
            "android": true,
            "com.miui.home": true,
            "com.miui.fliphome": false // 默认不启用这个模块
        };

        moduleStatusModifications = { ...defaultModuleStatus };
        console.log('✅ 使用默认模块状态:', moduleStatusModifications);
        console.log('✅ 默认模块数量:', Object.keys(moduleStatusModifications).length);
    }
}

/**
 * 保存模块状态到配置文件
 */
async function saveModuleStatusToConfig(): Promise<void> {
    if (!hasModifications) {
        return; // 没有修改就不保存
    }

    try {
        // 使用正确的配置文件路径 - 通过Tauri命令获取
        const { invoke } = await import('@tauri-apps/api/core');
        const configPath = await invoke('获取配置文件路径', { fileName: 'random_color_theme_config.json5' }) as string;

        let config: any = {};
        if (await exists(configPath)) {
            const { readTextFile } = await import('@tauri-apps/plugin-fs');
            const configContent = await readTextFile(configPath);
            config = JSON.parse(configContent.replace(/\/\*[\s\S]*?\*\/|\/\/.*$/gm, ''));
        }

        // 更新module对象
        config.module = { ...moduleStatusModifications };

        const { writeTextFile } = await import('@tauri-apps/plugin-fs');
        await writeTextFile(configPath, JSON.stringify(config, null, 4));

        console.log('模块状态已保存到配置文件');
        hasModifications = false; // 重置修改标记
    } catch (error) {
        console.error('保存模块状态到配置文件失败:', error);
    }
}

/**
 * 填充生成模块选择选项
 * @param dropdownMenu 下拉菜单元素
 */
async function populateGenerateModuleOptions(dropdownMenu: HTMLElement): Promise<void> {
    try {
        // 确保配置已加载
        if (Object.keys(moduleStatusModifications).length === 0) {
            console.log('模块配置为空，重新加载配置文件');
            await loadModuleStatusFromConfig();
        }

        // 从全局变量获取模块名称（对象的键）
        const moduleNames = Object.keys(moduleStatusModifications);
        console.log('当前模块列表:', moduleNames);
        console.log('当前模块状态:', moduleStatusModifications);

        if (moduleNames.length === 0) {
            dropdownMenu.innerHTML = '<div class="apply-theme-dropdown-item disabled">暂无可用模块</div>';
            return;
        }

        // 清空菜单内容
        dropdownMenu.innerHTML = '';

        // 创建固定的顶部控制区域
        const fixedControlsContainer = document.createElement('div');
        fixedControlsContainer.className = 'generate-module-fixed-controls';
        fixedControlsContainer.style.cssText = `
            position: sticky;
            top: 0;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(0, 0, 0, 0.06);
            padding: 16px 20px;
            z-index: 10;
        `;

        // 创建三按钮容器（垂直排列）
        const topButtonsContainer = document.createElement('div');
        topButtonsContainer.className = 'top-buttons-container';
        topButtonsContainer.style.cssText = `
            display: flex;
            flex-direction: column;
            gap: 8px;
            margin-bottom: 0;
        `;

        // 检查是否为特权用户
        const isPrivilegedUser = await checkPrivilegedUser();

        // 通用按钮样式 - macOS极简风格
        const buttonBaseStyle = `
            width: 100%;
            margin: 0;
            padding: 8px 12px;
            cursor: pointer;
            user-select: none;
            text-align: center;
            border-radius: 6px;
            font-size: 13px;
            font-weight: 400;
            transition: all 0.15s ease;
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', sans-serif;
            border: 1px solid rgba(0, 0, 0, 0.1);
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(255, 255, 255, 0.8);
        `;

        // 全选按钮 - macOS极简风格
        const selectAllItem = document.createElement('button');
        selectAllItem.className = 'apply-theme-dropdown-item select-all';
        selectAllItem.textContent = '全选';
        selectAllItem.dataset.action = 'select-all';
        selectAllItem.style.cssText = `
            ${buttonBaseStyle}
            color: #007AFF;
        `;

        // 生成按钮 - macOS极简风格
        const generateButton = document.createElement('button');
        generateButton.className = 'apply-theme-apply-btn';
        generateButton.textContent = '生成';
        generateButton.style.cssText = `
            ${buttonBaseStyle}
            background: #007AFF;
            color: white;
            border: 1px solid #007AFF;
            font-weight: 500;
        `;

        // 添加按钮（特权用户可见）- macOS极简风格
        let addButton: HTMLElement | null = null;
        if (isPrivilegedUser) {
            addButton = document.createElement('button');
            addButton.className = 'add-module-btn';
            addButton.textContent = '添加';
            addButton.style.cssText = `
                ${buttonBaseStyle}
                color: #34C759;
            `;
        }

        // 根据模块选择状态更新全选按钮
        const selectedCount = moduleNames.filter(moduleName => moduleStatusModifications[ moduleName ] === true).length;
        if (selectedCount === moduleNames.length) {
            // 全部选中
            selectAllItem.classList.add('selected');
            selectAllItem.style.background = '#007AFF';
            selectAllItem.style.color = 'white';
            selectAllItem.style.borderColor = '#007AFF';
        } else {
            // 部分选中或全部未选中
            selectAllItem.classList.remove('selected');
            selectAllItem.style.background = 'rgba(0, 122, 255, 0.06)';
            selectAllItem.style.color = '#007AFF';
            selectAllItem.style.borderColor = 'rgba(0, 122, 255, 0.15)';
        }

        // macOS风格hover效果
        selectAllItem.addEventListener('mouseenter', () => {
            if (selectAllItem.classList.contains('selected')) {
                selectAllItem.style.background = '#0056CC';
            } else {
                selectAllItem.style.background = 'rgba(0, 122, 255, 0.08)';
            }
        });

        selectAllItem.addEventListener('mouseleave', () => {
            if (selectAllItem.classList.contains('selected')) {
                selectAllItem.style.background = '#007AFF';
            } else {
                selectAllItem.style.background = 'rgba(255, 255, 255, 0.8)';
            }
        });

        generateButton.addEventListener('mouseenter', () => {
            generateButton.style.background = '#0056CC';
        });

        generateButton.addEventListener('mouseleave', () => {
            generateButton.style.background = '#007AFF';
        });

        // 添加按钮事件（如果存在）
        if (addButton) {
            addButton.addEventListener('mouseenter', () => {
                addButton!.style.background = 'rgba(52, 199, 89, 0.08)';
            });

            addButton.addEventListener('mouseleave', () => {
                addButton!.style.background = 'rgba(255, 255, 255, 0.8)';
            });

            // 添加按钮点击事件 - 弹窗方式
            addButton.addEventListener('click', () => {
                showAddModuleDialog(dropdownMenu);
            });
        }

        // 组装按钮到容器
        topButtonsContainer.appendChild(selectAllItem);
        topButtonsContainer.appendChild(generateButton);
        if (addButton) {
            topButtonsContainer.appendChild(addButton);
        }

        // 组装固定控制区域
        fixedControlsContainer.appendChild(topButtonsContainer);

        // 创建可滚动的模块列表区域
        const scrollableModuleList = document.createElement('div');
        scrollableModuleList.className = 'scrollable-module-list';
        scrollableModuleList.style.cssText = `
            max-height: 50vh;
            overflow-y: auto;
            padding: 8px 16px 30px 16px;
            box-sizing: border-box;
        `;

        // 使用文档片段来减少DOM重排
        const moduleFragment = document.createDocumentFragment();

        // 添加模块选项到文档片段
        moduleNames.forEach((moduleName) => {
            const item = document.createElement('div');
            // 根据moduleStatusModifications中的值设置选中状态
            const isSelected = moduleStatusModifications[ moduleName ] === true;
            item.className = `apply-theme-dropdown-item ${isSelected ? 'selected' : ''}`;
            item.dataset.module = moduleName;
            item.textContent = getModuleDisplayName(moduleName);
            item.style.cssText = `
                padding: 8px 12px;
                margin-bottom: 4px;
                border-radius: 6px;
                cursor: pointer;
                user-select: none;
            `;
            moduleFragment.appendChild(item);
        });

        // 将模块列表添加到可滚动区域
        scrollableModuleList.appendChild(moduleFragment);

        // 设置下拉菜单的基础样式 - 极简macOS风格
        dropdownMenu.style.cssText = `
            max-height: 60vh;
            overflow: hidden;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(40px);
        `;

        // 将固定控制区域和可滚动模块列表添加到下拉菜单
        dropdownMenu.appendChild(fixedControlsContainer);
        dropdownMenu.appendChild(scrollableModuleList);

        // 添加事件监听
        setupGenerateModuleEvents(dropdownMenu);

    } catch (error) {
        console.error('填充生成模块选项失败:', error);
        dropdownMenu.innerHTML = '<div class="apply-theme-dropdown-item disabled">加载模块失败</div>';
    }
}

/**
 * 显示添加模块对话框
 * @param dropdownMenu 下拉菜单元素
 */
function showAddModuleDialog(dropdownMenu: HTMLElement): void {
    // 创建对话框遮罩
    const overlay = document.createElement('div');
    overlay.className = 'add-module-dialog-overlay';
    overlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10000;
        backdrop-filter: blur(4px);
    `;

    // 创建对话框
    const dialog = document.createElement('div');
    dialog.className = 'add-module-dialog';
    dialog.style.cssText = `
        background: white;
        border-radius: 12px;
        padding: 24px;
        min-width: 320px;
        max-width: 400px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        animation: dialogFadeIn 0.2s ease-out;
    `;

    // 添加动画样式
    const style = document.createElement('style');
    style.textContent = `
        @keyframes dialogFadeIn {
            from {
                opacity: 0;
                transform: scale(0.95) translateY(-10px);
            }
            to {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
        }
    `;
    document.head.appendChild(style);

    // 标题
    const title = document.createElement('h3');
    title.textContent = '添加新模块';
    title.style.cssText = `
        margin: 0 0 16px 0;
        font-size: 16px;
        font-weight: 600;
        color: #333;
        font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', sans-serif;
    `;

    // 输入框
    const input = document.createElement('input');
    input.type = 'text';
    input.placeholder = '请输入模块名称';
    input.style.cssText = `
        width: 100%;
        padding: 12px 16px;
        border: 1px solid rgba(0, 0, 0, 0.15);
        border-radius: 8px;
        font-size: 14px;
        outline: none;
        margin-bottom: 20px;
        font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', sans-serif;
        box-sizing: border-box;
        transition: border-color 0.2s ease;
    `;

    // 添加输入框focus样式
    input.addEventListener('focus', () => {
        input.style.borderColor = '#007AFF';
        input.style.boxShadow = '0 0 0 3px rgba(0, 122, 255, 0.1)';
    });
    input.addEventListener('blur', () => {
        input.style.borderColor = 'rgba(0, 0, 0, 0.15)';
        input.style.boxShadow = 'none';
    });

    // 按钮容器
    const buttonContainer = document.createElement('div');
    buttonContainer.style.cssText = `
        display: flex;
        gap: 12px;
        justify-content: flex-end;
    `;

    // 取消按钮
    const cancelButton = document.createElement('button');
    cancelButton.textContent = '取消';
    cancelButton.style.cssText = `
        padding: 8px 16px;
        border: 1px solid rgba(0, 0, 0, 0.15);
        background: white;
        color: #666;
        border-radius: 6px;
        cursor: pointer;
        font-size: 13px;
        font-weight: 500;
        font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', sans-serif;
        transition: all 0.2s ease;
    `;

    // 确认按钮
    const confirmButton = document.createElement('button');
    confirmButton.textContent = '添加';
    confirmButton.style.cssText = `
        padding: 8px 16px;
        border: none;
        background: #007AFF;
        color: white;
        border-radius: 6px;
        cursor: pointer;
        font-size: 13px;
        font-weight: 500;
        font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', sans-serif;
        transition: all 0.2s ease;
        box-shadow: 0 1px 3px rgba(0, 122, 255, 0.25);
    `;

    // 按钮hover效果
    cancelButton.addEventListener('mouseenter', () => {
        cancelButton.style.background = '#f8f9fa';
        cancelButton.style.borderColor = 'rgba(0, 0, 0, 0.25)';
    });
    cancelButton.addEventListener('mouseleave', () => {
        cancelButton.style.background = 'white';
        cancelButton.style.borderColor = 'rgba(0, 0, 0, 0.15)';
    });

    confirmButton.addEventListener('mouseenter', () => {
        confirmButton.style.background = '#0056CC';
        confirmButton.style.boxShadow = '0 2px 8px rgba(0, 122, 255, 0.4)';
    });
    confirmButton.addEventListener('mouseleave', () => {
        confirmButton.style.background = '#007AFF';
        confirmButton.style.boxShadow = '0 1px 3px rgba(0, 122, 255, 0.25)';
    });

    // 组装对话框
    buttonContainer.appendChild(cancelButton);
    buttonContainer.appendChild(confirmButton);
    dialog.appendChild(title);
    dialog.appendChild(input);
    dialog.appendChild(buttonContainer);
    overlay.appendChild(dialog);

    // 添加到页面
    document.body.appendChild(overlay);

    // 自动聚焦输入框
    setTimeout(() => input.focus(), 100);

    // 关闭对话框函数
    const closeDialog = () => {
        document.body.removeChild(overlay);
    };

    // 事件监听
    cancelButton.addEventListener('click', closeDialog);

    overlay.addEventListener('click', (e) => {
        if (e.target === overlay) {
            closeDialog();
        }
    });

    // ESC键关闭
    const handleKeyDown = (e: KeyboardEvent) => {
        if (e.key === 'Escape') {
            closeDialog();
            document.removeEventListener('keydown', handleKeyDown);
        }
        if (e.key === 'Enter') {
            confirmButton.click();
        }
    };
    document.addEventListener('keydown', handleKeyDown);

    // 确认按钮事件
    confirmButton.addEventListener('click', async () => {
        const moduleName = input.value.trim();
        if (!moduleName) {
            messageError('请输入模块名称');
            return;
        }

        await handleAddModuleToConfig(moduleName, dropdownMenu);
        closeDialog();
        document.removeEventListener('keydown', handleKeyDown);
    });
}

/**
 * 处理添加模块到配置的逻辑
 * @param moduleName 模块名称
 * @param dropdownMenu 下拉菜单元素
 */
async function handleAddModuleToConfig(moduleName: string, dropdownMenu: HTMLElement): Promise<void> {
    try {
        // 检查模块是否已存在
        const existingItems = dropdownMenu.querySelectorAll('.apply-theme-dropdown-item:not(.select-all)');
        const exists = Array.from(existingItems).some(item =>
            (item as HTMLElement).dataset.module === moduleName
        );

        if (exists) {
            messageError('模块已存在');
            return;
        }

        // 将新模块添加到全局变量中，默认值为true
        moduleStatusModifications[ moduleName ] = true;
        hasModifications = true;

        console.log(`模块 ${moduleName} 已添加到内存配置`);

        // 创建新的模块项
        const newItem = document.createElement('div');
        newItem.className = 'apply-theme-dropdown-item selected';
        newItem.dataset.module = moduleName;
        newItem.textContent = getModuleDisplayName(moduleName);
        newItem.style.cssText = `
            padding: 8px 12px;
            margin-bottom: 4px;
            border-radius: 6px;
            cursor: pointer;
            user-select: none;
        `;

        // 插入到可滚动模块列表中
        const scrollableModuleList = dropdownMenu.querySelector('.scrollable-module-list');
        if (scrollableModuleList) {
            scrollableModuleList.appendChild(newItem);
        }

        // 为新项添加点击事件
        newItem.addEventListener('click', () => {
            newItem.classList.toggle('selected');

            // 更新全选状态
            const moduleItems = dropdownMenu.querySelectorAll('.scrollable-module-list .apply-theme-dropdown-item') as NodeListOf<HTMLElement>;
            const selectAllItem = dropdownMenu.querySelector('.select-all') as HTMLElement;
            const selectedCount = Array.from(moduleItems).filter(item => item.classList.contains('selected')).length;

            if (selectedCount === moduleItems.length) {
                selectAllItem.classList.add('selected');
                selectAllItem.style.background = 'linear-gradient(135deg, #007AFF 0%, #0056CC 100%)';
                selectAllItem.style.color = 'white';
                selectAllItem.style.borderColor = '#007AFF';
                selectAllItem.style.boxShadow = '0 2px 8px rgba(0, 122, 255, 0.3)';
            } else {
                selectAllItem.classList.remove('selected');
                selectAllItem.style.background = 'linear-gradient(135deg, rgba(0, 122, 255, 0.08) 0%, rgba(0, 122, 255, 0.05) 100%)';
                selectAllItem.style.color = '#007AFF';
                selectAllItem.style.borderColor = 'rgba(0, 122, 255, 0.2)';
                selectAllItem.style.boxShadow = '0 2px 8px rgba(0, 122, 255, 0.1)';
            }

            // 保存选择状态
            saveModuleSelections(moduleItems);
        });

        // 更新事件监听器（重新获取模块列表）
        const moduleItems = dropdownMenu.querySelectorAll('.scrollable-module-list .apply-theme-dropdown-item') as NodeListOf<HTMLElement>;
        saveModuleSelections(moduleItems);

        messageSuccess(`模块 ${moduleName} 已添加成功`);

    } catch (error) {
        console.error('添加模块失败:', error);
        messageError(`添加模块失败: ${error}`);
    }
}

/**
 * 生成选中的模块
 * @param selectedModules 选中的模块列表
 */
async function generateSelectedModules(selectedModules: string[]): Promise<void> {
    try {
        // 使用正确的配置文件路径读取原始配置
        const { invoke } = await import('@tauri-apps/api/core');
        const configPath = await invoke('获取配置文件路径', { fileName: 'random_color_theme_config.json5' }) as string;

        if (await exists(configPath)) {
            const { readTextFile } = await import('@tauri-apps/plugin-fs');
            const configContent = await readTextFile(configPath);
            const originalConfig = JSON.parse(configContent.replace(/\/\*[\s\S]*?\*\/|\/\/.*$/gm, ''));

            // 获取所有可用的模块名称
            let allModules: string[] = [];
            if (originalConfig.module) {
                if (Array.isArray(originalConfig.module)) {
                    allModules = originalConfig.module;
                } else if (typeof originalConfig.module === 'object') {
                    allModules = Object.keys(originalConfig.module);
                }
            }

            // 创建临时配置对象（不修改原始配置文件）
            const tempConfig = {
                ...originalConfig,
                module: allModules, // 确保module是数组格式供生成函数使用
                module_status: {} as { [ key: string ]: boolean }
            };

            // 将所有模块设为禁用
            allModules.forEach((moduleName: string) => {
                tempConfig.module_status[ moduleName ] = false;
            });

            // 启用选中的模块
            selectedModules.forEach(moduleName => {
                tempConfig.module_status[ moduleName ] = true;
            });

            console.log('临时配置对象:', tempConfig);
            console.log('启用的模块:', selectedModules);

            // 调用生成函数，传递临时配置
            await random_color_theme(tempConfig);

            // 生成主题后自动刷新 data.json 和页面颜色数据
            if (window.reloadDataJsonAndRefresh) {
                await window.reloadDataJsonAndRefresh();
            }
        } else {
            throw new Error('配置文件不存在');
        }

    } catch (error) {
        console.error('生成选中模块失败:', error);
        throw error;
    }
}

/**
 * 保存模块选择状态到全局变量
 * @param moduleItems 模块项目列表
 */
function saveModuleSelections(moduleItems: NodeListOf<HTMLElement>): void {
    moduleItems.forEach(item => {
        const moduleName = item.dataset.module;
        if (moduleName) {
            const isSelected = item.classList.contains('selected');
            moduleStatusModifications[ moduleName ] = isSelected;
            hasModifications = true;
        }
    });
}

/**
 * 更新截图显示区域和预览区域
 * 在主题生成/应用完成后调用，确保UI与实际文件状态同步
 */
async function updateScreenshotAndPreviewArea(): Promise<void> {
    try {
        console.log('正在更新截图显示区域...');
        await renderScreenshots();

        // 如果当前选中的截图已被删除，清除预览区域
        if (currentSelectedScreenshot) {
            const screenshotPath = await join(getPath(), currentSelectedScreenshot);
            if (!await exists(screenshotPath)) {
                console.log('当前选中的截图已被删除，清除预览区域');
                currentSelectedScreenshot = null;
                const existingPreview = document.querySelector('.central-preview-image');
                if (existingPreview) {
                    existingPreview.remove();
                }
            }
        }

        console.log('截图显示区域更新完成');
    } catch (error) {
        console.error('更新截图显示区域失败:', error);
    }
}

/**
 * 设置生成模块的事件监听
 * @param dropdownMenu 下拉菜单元素
 */
function setupGenerateModuleEvents(dropdownMenu: HTMLElement): void {
    const selectAllItem = dropdownMenu.querySelector('.select-all') as HTMLElement;
    const moduleItems = dropdownMenu.querySelectorAll('.scrollable-module-list .apply-theme-dropdown-item') as NodeListOf<HTMLElement>;
    const generateButton = dropdownMenu.querySelector('.apply-theme-apply-btn') as HTMLButtonElement;

    // 全选/取消全选事件
    selectAllItem?.addEventListener('click', () => {
        const isAllSelected = selectAllItem.classList.contains('selected');

        if (isAllSelected) {
            // 取消全选
            selectAllItem.classList.remove('selected');
            selectAllItem.style.background = 'rgba(0, 122, 255, 0.06)';
            selectAllItem.style.color = '#007AFF';
            selectAllItem.style.borderColor = 'rgba(0, 122, 255, 0.15)';
            moduleItems.forEach(item => item.classList.remove('selected'));
        } else {
            // 全选
            selectAllItem.classList.add('selected');
            selectAllItem.style.background = '#007AFF';
            selectAllItem.style.color = 'white';
            selectAllItem.style.borderColor = '#007AFF';
            moduleItems.forEach(item => item.classList.add('selected'));
        }

        // 保存选择状态
        saveModuleSelections(moduleItems);
    });

    // 单个模块选择事件
    moduleItems.forEach(item => {
        item.addEventListener('click', () => {
            // 切换选中状态
            item.classList.toggle('selected');

            // 检查是否所有模块都被选中
            const selectedModules = Array.from(moduleItems).filter(module => module.classList.contains('selected'));
            const allSelected = selectedModules.length === moduleItems.length;

            if (allSelected) {
                selectAllItem.classList.add('selected');
                selectAllItem.style.background = '#007AFF';
                selectAllItem.style.color = 'white';
                selectAllItem.style.borderColor = '#007AFF';
            } else {
                selectAllItem.classList.remove('selected');
                selectAllItem.style.background = 'rgba(0, 122, 255, 0.06)';
                selectAllItem.style.color = '#007AFF';
                selectAllItem.style.borderColor = 'rgba(0, 122, 255, 0.15)';
            }

            // 保存选择状态
            saveModuleSelections(moduleItems);
        });
    });

    // 生成按钮事件
    generateButton?.addEventListener('click', async () => {
        try {
            const selectedModules = Array.from(moduleItems)
                .filter(item => item.classList.contains('selected'))
                .map(item => item.dataset.module)
                .filter(Boolean) as string[];

            if (selectedModules.length === 0) {
                messageError('请至少选择一个模块');
                return;
            }

            console.log('开始生成选中模块的随机颜色主题:', selectedModules);
            messageLoading('正在检查设备连接...');

            // 智能设备检查和选择
            const deviceOk = await checkAndSelectDevice();
            if (!deviceOk) {
                return;
            }

            messageLoading(`正在生成 ${selectedModules.length} 个模块的随机颜色主题...`);

            // 调用生成函数，通过修改配置文件实现选择性生成
            await generateSelectedModules(selectedModules);

            console.log('生成随机颜色主题完成');
            // messageSuccess 已在 generateSelectedModules 或 random_color_theme 函数内部调用

            // 生成完成后更新截图显示区域和预览区域
            await updateScreenshotAndPreviewArea();

            // 生成成功后隐藏菜单
            dropdownMenu.style.display = 'none';
            const dropdownButton = dropdownMenu.parentElement?.querySelector('.apply-theme-dropdown-btn') as HTMLElement;
            if (dropdownButton) {
                dropdownButton.textContent = '▼';
                dropdownButton.classList.remove('active');
            }

        } catch (error) {
            console.error('生成随机颜色主题失败:', error);
            messageError(`生成随机颜色主题失败: ${error}`);
        }
    });
}

/**
 * 填充模块选择选项
 * @param dropdownMenu 下拉菜单元素
 * @param versionType 版本类型
 * @param buttonText 按钮文本
 */
async function populateModuleOptions(dropdownMenu: HTMLElement, versionType: 'color' | 'image' | 'full'): Promise<void> {
    try {
        // 使用文档片段来减少DOM重排
        const fragment = document.createDocumentFragment();

        const modules = await getAvailableModules(versionType);

        if (modules.length === 0) {
            dropdownMenu.innerHTML = '<div class="apply-theme-dropdown-item disabled">暂无可用模块</div>';
            return;
        }

        // 清空菜单内容
        dropdownMenu.innerHTML = '';

        // 添加模块选项到文档片段
        modules.forEach(moduleName => {
            const item = document.createElement('div');
            item.className = 'apply-theme-dropdown-item';
            item.textContent = getModuleDisplayName(moduleName);

            // 点击模块直接应用
            item.addEventListener('click', async () => {
                try {
                    switch (versionType) {
                        case 'color':
                            await applyColorValueTheme([ moduleName ]);
                            break;
                        case 'image':
                            await applyImageTheme([ moduleName ]);
                            break;
                        case 'full':
                            await applyFullTheme([ moduleName ]);
                            break;
                    }

                    // 应用完成后更新截图显示区域和预览区域
                    await updateScreenshotAndPreviewArea();

                    // 应用成功后隐藏菜单
                    dropdownMenu.style.display = 'none';
                    const dropdownButton = dropdownMenu.parentElement?.querySelector('.apply-theme-dropdown-btn') as HTMLElement;
                    if (dropdownButton) {
                        dropdownButton.textContent = '▼';
                        dropdownButton.classList.remove('active');
                    }
                } catch (error) {
                    console.error(`应用模块 ${moduleName} 失败:`, error);
                    messageError(`应用模块 ${moduleName} 失败`);
                }
            });

            fragment.appendChild(item);
        });

        // 一次性添加所有模块到DOM
        dropdownMenu.appendChild(fragment);

        // 设置下拉菜单的基础样式 - 极简macOS风格
        dropdownMenu.style.cssText = `
            max-height: 60vh;
            overflow-y: auto;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(40px);
            padding: 8px;
        `;

    } catch (error) {
        console.error('填充模块选项失败:', error);
        dropdownMenu.innerHTML = '<div class="apply-theme-dropdown-item disabled">加载模块失败</div>';

        // 即使失败也要设置样式
        dropdownMenu.style.cssText = `
            max-height: 60vh;
            overflow-y: auto;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(40px);
            padding: 8px;
        `;
    }
}

/**
 * 初始化截图功能
 */
export async function init(): Promise<void> {
    try {
        // 获取和创建截图目录
        const rootDir = await get_the_root_directory_path('random_color_theme');
        screenshotPath = await join(rootDir, 'screenshot');

        if (!await exists(screenshotPath)) {
            try {
                await mkdir(screenshotPath, { recursive: true });
            } catch (error) {
                console.error('创建截图目录失败:', error);
                // 即使创建目录失败，也继续初始化，因为目录可能已经存在
                // 或者会在后续操作中自动创建
            }
        }
    } catch (error) {
        console.error('初始化截图目录失败:', error);
        messageError(`初始化失败: ${error}`);
    }
}

/**
 * 更新截图选择状态和预览图
 */
function updateScreenshotSelection(selectedItem: Element, imgSrc: string) {
    try {
        // 1. 批量移除所有激活状态，避免多次查询
        const activeItems = document.querySelectorAll('.screenshot-item.active');
        activeItems.forEach(item => item.classList.remove('active'));

        // 2. 标记当前项为激活状态
        selectedItem.classList.add('active');

        // 3. 创建或更新预览图
        createOrUpdatePreview(imgSrc);

        console.log('截图选择状态已更新');
    } catch (error) {
        console.error('更新截图选择状态失败:', error);
    }
}

/**
 * 创建或更新预览图
 */
async function createOrUpdatePreview(imgSrc: string): Promise<void> {
    try {
        // 缓存主容器，避免重复查询
        const mainContainer = document.querySelector('.color-search-container') || document.body;

        // 停用取色器（如果正在使用）
        try {
            const { resetColorPickerState } = await import('./color_picker');
            resetColorPickerState();
        } catch (error) {
            console.log('取色器模块未加载或停用失败:', error);
        }

        // 先移除所有已存在的预览图，避免重复点击造成叠加
        const existingPreviews = document.querySelectorAll('.central-preview-image');
        existingPreviews.forEach(preview => preview.remove());

        // 创建预览图容器
        const previewImageContainer = document.createElement('div');
        previewImageContainer.className = 'central-preview-image';

        // 创建预览图
        const previewImg = document.createElement('img');
        previewImg.src = imgSrc;
        previewImg.className = 'central-preview-img';

        // 优化图片加载，添加错误处理
        previewImg.onerror = () => {
            console.error('预览图加载失败:', imgSrc);
            previewImg.alt = '图片加载失败';
        };


        // 添加到容器
        previewImageContainer.appendChild(previewImg);

        // 添加预览图到主容器
        mainContainer.appendChild(previewImageContainer);

        console.log('预览图已创建/更新');
    } catch (error) {
        console.error('创建预览图失败:', error);
    }
}

/**
 * 获取截图保存路径
 */
function getPath(): string {
    if (!screenshotPath) {
        throw new Error('截图功能未初始化，请先调用init()');
    }
    return screenshotPath;
}

/**
 * 添加一张截图
 * @returns 成功时返回文件路径，失败返回undefined
 */
export async function addScreenshot(): Promise<void> {
    try {
        // 确保初始化
        if (!screenshotPath) await init();

        // 获取提示元素
        const guideElement = document.querySelector('.add-screenshot-guide') as HTMLElement;

        // 调用截图功能
        const filePaths = await window.截屏(getPath());

        if (filePaths && filePaths.length > 0) {
            messageSuccess(`${filePaths.length}张截图已添加`);

            // 获取容器
            const container = document.getElementById('screenshots-container');
            if (!container) return;

            // 检查是否有"暂无截图"提示，并只在第一次添加时处理
            const noScreenshotsElement = container.querySelector('.no-screenshots');
            if (noScreenshotsElement) {
                container.innerHTML = '';
            }

            // 隐藏添加截图提示
            if (guideElement) {
                guideElement.style.display = 'none';
            }

            for (const filePath of filePaths) {
                let fileName = '';
                try {
                    // 提取文件名（移除路径部分）
                    fileName = filePath.split(/[\/\\]/).pop() || '';

                    // 计算新添加的截图序号
                    const screenshotElements = container.querySelectorAll('.screenshot-item');
                    const screenshotNumber = screenshotElements.length + 1;

                    // 创建新的截图元素并添加到容器
                    const imgContainer = document.createElement('div');
                    imgContainer.className = 'screenshot-item';
                    imgContainer.style.opacity = '0';
                    imgContainer.style.transform = 'scale(0.95)';
                    imgContainer.style.transition = 'opacity 0.3s ease, transform 0.3s ease';

                    // 添加数据属性
                    imgContainer.dataset.index = screenshotNumber.toString();
                    imgContainer.dataset.filename = fileName;
                    imgContainer.dataset.loaded = 'loading';

                    // 直接读取新添加的图片
                    const imageData = await readImageAsDataURL(filePath);

                    // 创建图片容器
                    const imgWrapper = document.createElement('div');
                    imgWrapper.className = 'screenshot-img-container';

                    // 创建图片元素
                    const img = document.createElement('img');
                    img.alt = fileName;
                    img.loading = 'eager'; // 立即加载，不使用懒加载
                    img.className = 'screenshot-img';

                    // 图片加载完成后显示容器并自动选中
                    img.onload = () => {
                        // 立即显示
                        imgContainer.style.opacity = '1';
                        imgContainer.style.transform = 'scale(1)';
                        imgContainer.dataset.loaded = 'loaded';

                        // 更新当前选中的截图
                        currentSelectedScreenshot = fileName;

                        // 更新UI状态
                        updateScreenshotSelection(imgContainer, img.src);
                    };

                    if (imageData) {
                        // 直接设置图片源，无需使用占位图
                        img.src = imageData;
                    }
                    // 将图片添加到图片容器
                    imgWrapper.appendChild(img);

                    // 创建底部信息容器
                    const footer = document.createElement('div');
                    footer.className = 'screenshot-footer';

                    // 添加序号
                    const number = document.createElement('span');
                    number.className = 'screenshot-number';
                    number.textContent = `${screenshotNumber}`;

                    // 添加删除按钮
                    const deleteBtn = document.createElement('button');
                    deleteBtn.className = 'screenshot-delete-btn';
                    deleteBtn.textContent = '删除';
                    deleteBtn.addEventListener('click', async (e) => {
                        e.stopPropagation(); // 阻止事件冒泡
                        await deleteScreenshot(fileName);
                    });

                    // 将元素添加到底部信息容器
                    footer.appendChild(number);
                    footer.appendChild(deleteBtn);

                    // 将元素添加到容器
                    imgContainer.appendChild(imgWrapper);
                    imgContainer.appendChild(footer);

                    // 将新截图添加到容器末尾
                    container.appendChild(imgContainer);

                    // 滚动到新添加的截图位置
                    setTimeout(() => {
                        imgContainer.scrollIntoView({ behavior: 'smooth', block: 'nearest', inline: 'end' });
                    }, 100);
                } catch (error) {
                    console.error(`渲染新添加的截图 ${fileName || filePath} 失败:`, error);
                }
            }
        }
    } catch (error) {
        console.error('截图失败:', error);
        messageError(`截图失败: ${error}`);
    }
}

/**
 * 删除指定截图
 * @param fileName 文件名
 * @returns 是否删除成功
 */
export async function deleteScreenshot(fileName: string): Promise<boolean> {
    // 防止并发删除操作
    if (isDeleting) {
        console.warn('删除操作正在进行中，请稍后再试');
        return false;
    }

    isDeleting = true;

    try {
        const path = getPath();
        // 修复路径重复问题，确保文件名不包含完整路径
        const fileNameOnly = fileName.split(/[\/\\]/).pop() || fileName;
        const filePath = await join(path, fileNameOnly);

        // 检查文件是否存在
        if (!await exists(filePath)) {
            console.warn(`文件不存在: ${filePath}`);
            return false;
        }

        // 获取截图元素
        const container = document.getElementById('screenshots-container');
        // 使用文件名而不是路径来查找元素
        const screenshotElement = container?.querySelector(`.screenshot-item[data-filename="${fileNameOnly}"]`) as HTMLElement;

        // 检查是否需要清除当前选中状态
        const isCurrentSelected = currentSelectedScreenshot === fileNameOnly;

        // 添加渐隐动画
        if (screenshotElement) {
            screenshotElement.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
            screenshotElement.style.opacity = '0';
            screenshotElement.style.transform = 'scale(0.9)';

            // 等待动画完成后从DOM中移除
            await new Promise<void>((resolve) => {
                setTimeout(async () => {
                    try {
                        // 删除图片文件
                        await remove(filePath);

                        // 删除可能存在的关联JSON文件 - 删除失败不阻塞进程
                        try {
                            const jsonPath = await join(path, `${fileNameOnly.replace(/\.\w+$/i, '')}.json`);
                            if (await exists(jsonPath)) {
                                await remove(jsonPath);
                            }
                        } catch (jsonError) {
                            console.error(`删除关联JSON文件失败，但不影响主要流程: ${jsonError}`);
                        }

                        screenshotElement.remove();

                        // 检查是否还有截图
                        const remainingScreenshots = container?.querySelectorAll('.screenshot-item');

                        if (!remainingScreenshots || remainingScreenshots.length === 0) {
                            // 如果没有截图，显示"暂无截图"提示
                            if (container) {
                                container.innerHTML = '<div class="no-screenshots">暂无截图</div>';

                                // 显示添加截图提示
                                const guideElement = document.querySelector('.add-screenshot-guide') as HTMLElement;
                                if (guideElement) {
                                    guideElement.style.display = 'flex';
                                }

                                // 清除当前选中的截图
                                currentSelectedScreenshot = null;

                                // 停用取色器（如果正在使用）
                                try {
                                    const { resetColorPickerState } = await import('./color_picker');
                                    resetColorPickerState();
                                } catch (error) {
                                    console.log('取色器模块未加载或停用失败:', error);
                                }
                            }
                        } else {
                            // 重新排序序号
                            remainingScreenshots.forEach((item, index) => {
                                const numberElement = item.querySelector('.screenshot-number');
                                if (numberElement) {
                                    numberElement.textContent = `${index + 1}`;
                                }
                                // 更新索引数据属性
                                (item as HTMLElement).dataset.index = index.toString();
                            });

                            // 如果删除的是当前选中的截图，自动选中第一个可用的截图
                            if (isCurrentSelected) {
                                // 停用取色器（因为要切换截图）
                                try {
                                    const { resetColorPickerState } = await import('./color_picker');
                                    resetColorPickerState();
                                } catch (error) {
                                    console.log('取色器模块未加载或停用失败:', error);
                                }

                                const firstRemaining = remainingScreenshots[ 0 ] as HTMLElement;
                                const firstImg = firstRemaining.querySelector('.screenshot-img') as HTMLImageElement;
                                const filename = firstRemaining.dataset.filename;

                                if (filename && firstImg && firstImg.src && !firstImg.src.includes('data:image/svg+xml')) {
                                    currentSelectedScreenshot = filename;
                                    updateScreenshotSelection(firstRemaining, firstImg.src);
                                } else {
                                    currentSelectedScreenshot = null;
                                }
                            }
                        }

                        messageSuccess('截图已删除');
                        resolve();
                    } catch (error) {
                        console.error(`删除文件失败: ${error}`);
                        messageError(`删除文件失败: ${error}`);
                        resolve();
                    }
                }, 300);
            });

            return true;
        } else {
            // 如果没有找到对应的DOM元素，回退到完全重新渲染
            try {
                await remove(filePath);

                // 删除可能存在的关联JSON文件 - 删除失败不阻塞进程
                try {
                    const jsonPath = await join(path, `${fileNameOnly.replace(/\.\w+$/i, '')}.json`);
                    if (await exists(jsonPath)) {
                        await remove(jsonPath);
                    }
                } catch (jsonError) {
                    console.error(`删除关联JSON文件失败，但不影响主要流程: ${jsonError}`);
                }

                // 如果删除的是当前选中的截图，清除选中状态
                if (isCurrentSelected) {
                    currentSelectedScreenshot = null;
                }

                await renderScreenshots(); // 删除后重新渲染
                messageSuccess('截图已删除');
                return true;
            } catch (error) {
                console.error(`删除文件失败: ${error}`);
                messageError(`删除文件失败: ${error}`);
                return false;
            }
        }
    } catch (error) {
        console.error(`删除截图失败:`, error);
        messageError(`删除失败: ${error}`);
        return false;
    } finally {
        // 确保总是释放锁定
        isDeleting = false;
    }
}

/**
 * 清空所有截图
 * @returns 是否全部清除成功
 */
export async function clearAllScreenshots(): Promise<boolean> {
    try {
        const path = getPath();

        // 获取所有截图文件
        const files = await readDir(path);
        const imageFiles = files.filter(file =>
            file.name && /\.(png|jpe?g)$/i.test(file.name)
        );

        if (imageFiles.length === 0) {
            return true;
        }

        // 获取容器元素
        const container = document.getElementById('screenshots-container');
        const screenshotElements = container?.querySelectorAll('.screenshot-item');

        // 清除当前选中状态
        currentSelectedScreenshot = null;

        // 停用取色器（如果正在使用）
        try {
            const { resetColorPickerState } = await import('./color_picker');
            resetColorPickerState();
        } catch (error) {
            console.log('取色器模块未加载或停用失败:', error);
        }

        // 添加渐隐动画到所有截图
        if (screenshotElements && screenshotElements.length > 0) {
            screenshotElements.forEach((element) => {
                (element as HTMLElement).style.transition = 'opacity 0.3s ease, transform 0.3s ease';
                (element as HTMLElement).style.opacity = '0';
                (element as HTMLElement).style.transform = 'scale(0.9)';
            });

            // 等待动画完成后执行删除操作
            setTimeout(async () => {
                // 逐个删除所有截图文件
                let allSuccess = true;
                for (const file of imageFiles) {
                    if (file.name) {
                        try {
                            const filePath = await join(path, file.name);
                            await remove(filePath);

                            // 删除可能存在的关联JSON文件 - 删除失败不阻塞进程
                            try {
                                const jsonPath = await join(path, `${file.name.replace(/\.\w+$/i, '')}.json`);
                                if (await exists(jsonPath)) {
                                    await remove(jsonPath);
                                }
                            } catch (jsonError) {
                                console.error(`删除关联JSON文件失败，但不影响主要流程: ${jsonError}`);
                            }
                        } catch (e) {
                            console.error(`删除文件 ${file.name} 失败:`, e);
                            allSuccess = false;
                        }
                    }
                }

                // 清空容器
                if (container) {
                    container.innerHTML = '<div class="no-screenshots">暂无截图</div>';

                    // 显示添加截图提示
                    const guideElement = document.querySelector('.add-screenshot-guide') as HTMLElement;
                    if (guideElement) {
                        guideElement.style.display = 'flex';
                    }
                }

                // 清除预览区域
                const existingPreview = document.querySelector('.central-preview-image');
                if (existingPreview) {
                    existingPreview.remove();
                }

                if (allSuccess) {
                    messageSuccess('所有截图已清除');
                }
            }, 300);

            return true;
        } else {
            // 回退到原始方法
            let allSuccess = true;
            for (const file of imageFiles) {
                if (file.name) {
                    try {
                        const filePath = await join(path, file.name);
                        await remove(filePath);

                        // 删除可能存在的关联JSON文件 - 删除失败不阻塞进程
                        try {
                            const jsonPath = await join(path, `${file.name.replace(/\.\w+$/i, '')}.json`);
                            if (await exists(jsonPath)) {
                                await remove(jsonPath);
                            }
                        } catch (jsonError) {
                            console.error(`删除关联JSON文件失败，但不影响主要流程: ${jsonError}`);
                        }
                    } catch (e) {
                        console.error(`删除文件 ${file.name} 失败:`, e);
                        allSuccess = false;
                    }
                }
            }

            // 清空容器并显示"暂无截图"提示
            if (container) {
                container.innerHTML = '<div class="no-screenshots">暂无截图</div>';

                // 显示添加截图提示
                const guideElement = document.querySelector('.add-screenshot-guide') as HTMLElement;
                if (guideElement) {
                    guideElement.style.display = 'flex';
                }
            }

            // 清除预览区域
            const existingPreview = document.querySelector('.central-preview-image');
            if (existingPreview) {
                existingPreview.remove();
            }

            if (allSuccess) {
                messageSuccess('所有截图已清除');
            }

            return allSuccess;
        }
    } catch (error) {
        console.error('清除所有截图失败:', error);
        messageError(`清除失败: ${error}`);
        return false;
    }
}

// 读取并排序截图文件
async function readAndSortScreenshots() {
    try {
        // 获取目录路径
        const path = getPath();

        // 读取目录下所有文件
        const files = await readDir(path);

        // 过滤出图片文件并按时间戳排序
        const imageFiles = files
            .filter(file => file.name && /\.(png|jpe?g)$/i.test(file.name))
            .sort((a, b) => {
                // 从文件名中提取时间戳
                const getTimestamp = (filename: string) => {
                    const match = filename.match(/(\d+)\.(png|jpe?g)$/i);
                    return match ? parseInt(match[ 1 ]) : 0;
                };

                const timeA = getTimestamp(a.name || '');
                const timeB = getTimestamp(b.name || '');

                return timeA - timeB;
            });

        return imageFiles;

    } catch (error) {
        console.error('读取并排序截图文件失败:', error);
        return [];
    }
}

/**
 * 读取图片文件并转换为Data URL
 * 直接读取图片文件的二进制数据，不进行压缩处理，提高加载速度
 * @param filePath 图片文件路径
 * @returns 返回Data URL字符串
 */
async function readImageAsDataURL(filePath: string): Promise<string | null> {
    try {
        // 提取文件名，避免路径问题
        const fileName = filePath.split(/[\/\\]/).pop();
        if (!fileName) {
            console.error('无效的文件路径:', filePath);
            return null;
        }

        // 确保使用正确的路径
        const fullPath = await join(getPath(), fileName);

        // 使用Tauri的文件读取API读取图片文件
        const fs = await import('@tauri-apps/plugin-fs');
        // 检查文件是否存在
        if (!await fs.exists(fullPath)) {
            console.error(`文件不存在: ${fullPath}`);
            return null;
        }

        // 读取图片文件的二进制数据
        const data = await fs.readFile(fullPath);

        // 检查数据大小，避免加载过大的文件
        if (data.length > 10 * 1024 * 1024) { // 10MB限制
            console.warn(`文件过大，跳过加载: ${fullPath} (${(data.length / 1024 / 1024).toFixed(2)}MB)`);
            return null;
        }

        // 检测文件类型并确定MIME类型
        let mimeType = 'image/png'; // 默认MIME类型

        // 通过文件扩展名判断MIME类型
        const lowerPath = fullPath.toLowerCase();
        if (lowerPath.endsWith('.jpg') || lowerPath.endsWith('.jpeg')) {
            mimeType = 'image/jpeg';
        } else if (lowerPath.endsWith('.png')) {
            mimeType = 'image/png';
        }

        // 将二进制数据转换为base64编码的Data URL
        // 使用分片处理大数据，避免字符串拼接导致的内存问题
        const chunkSize = 32 * 1024; // 32KB 分片
        const chunks: string[] = [];
        const uint8Array = new Uint8Array(data);

        for (let i = 0; i < uint8Array.length; i += chunkSize) {
            const chunk = uint8Array.slice(i, i + chunkSize);
            chunks.push(String.fromCharCode(...chunk));
        }

        const base64Data = btoa(chunks.join(''));

        return `data:${mimeType};base64,${base64Data}`;
    } catch (error) {
        console.error('读取图片文件失败:', error);
        return null;
    }
}

/**
 * 渲染截图到容器中，优化性能
 */
export async function renderScreenshots(): Promise<void> {
    try {
        const container = document.getElementById('screenshots-container');
        // 获取提示元素
        const guideElement = document.querySelector('.add-screenshot-guide') as HTMLElement;

        if (!container) {
            console.warn('未找到截图容器元素');
            return;
        }

        // 清空容器
        container.innerHTML = '';

        // 获取排序后的截图文件
        const imageFiles = await readAndSortScreenshots();

        // 根据截图数量显示或隐藏提示
        if (imageFiles.length === 0) {
            container.innerHTML = '<div class="no-screenshots">暂无截图</div>';
            // 显示提示元素
            if (guideElement) {
                guideElement.style.display = 'flex';
            }
            return;
        } else {
            // 隐藏提示元素
            if (guideElement) {
                guideElement.style.display = 'none';
            }
        }

        // 创建批量加载器，每批处理5张图片
        const batchSize = 5;
        const batches = Math.ceil(imageFiles.length / batchSize);

        // 跟踪第一个完全加载的截图
        let firstLoadedItem: HTMLElement | null = null;

        // 预先创建所有截图容器元素
        for (let i = 0; i < imageFiles.length; i++) {
            const file = imageFiles[ i ];
            if (!file.name) continue;

            try {
                // 从完整路径中提取文件名
                const fileName = file.name.split(/[\/\\]/).pop() || file.name;

                // 创建截图容器
                const imgContainer = document.createElement('div');
                imgContainer.className = 'screenshot-item';
                // 添加初始透明度和缩放效果
                imgContainer.style.opacity = '0';
                imgContainer.style.transform = 'scale(0.95)';
                imgContainer.style.transition = 'opacity 0.3s ease, transform 0.3s ease';

                // 添加数据属性，用于稍后加载图片
                imgContainer.dataset.index = i.toString();
                imgContainer.dataset.filename = fileName;
                imgContainer.dataset.batchIndex = Math.floor(i / batchSize).toString();

                // 创建图片容器
                const imgWrapper = document.createElement('div');
                imgWrapper.className = 'screenshot-img-container';

                // 创建更美观的占位图片元素
                const img = document.createElement('img');
                img.alt = fileName;
                img.loading = 'lazy'; // 启用浏览器的原生懒加载

                // 改进占位图，使用更美观的渐变效果
                img.src = 'data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" width="80" height="180" viewBox="0 0 80 180"%3E%3Cdefs%3E%3ClinearGradient id="grad" x1="0%25" y1="0%25" x2="100%25" y2="100%25"%3E%3Cstop offset="0%25" style="stop-color:%23f5f5f5;stop-opacity:1" /%3E%3Cstop offset="100%25" style="stop-color:%23e0e0e0;stop-opacity:1" /%3E%3C/linearGradient%3E%3C/defs%3E%3Crect width="80" height="180" fill="url(%23grad)" /%3E%3Ctext x="40" y="90" font-family="Arial" font-size="10" text-anchor="middle" fill="%23999"%3E加载中...%3C/text%3E%3C/svg%3E';
                img.className = 'screenshot-img';

                // 将图片添加到图片容器
                imgWrapper.appendChild(img);

                // 创建底部信息容器
                const footer = document.createElement('div');
                footer.className = 'screenshot-footer';

                // 添加序号
                const number = document.createElement('span');
                number.className = 'screenshot-number';
                number.textContent = `${i + 1}`;

                // 添加删除按钮
                const deleteBtn = document.createElement('button');
                deleteBtn.className = 'screenshot-delete-btn';
                deleteBtn.textContent = '删除';
                deleteBtn.addEventListener('click', async (e) => {
                    e.stopPropagation(); // 阻止事件冒泡
                    await deleteScreenshot(fileName);
                });

                // 将元素添加到底部信息容器
                footer.appendChild(number);
                footer.appendChild(deleteBtn);

                // 将元素添加到容器
                imgContainer.appendChild(imgWrapper);
                imgContainer.appendChild(footer);
                container.appendChild(imgContainer);

                // 检查是否是当前选中的截图
                if (currentSelectedScreenshot === fileName) {
                    imgContainer.classList.add('active');
                }

                // 渐变显示容器
                setTimeout(() => {
                    imgContainer.style.opacity = '1';
                    imgContainer.style.transform = 'scale(1)';
                }, 10 + i * 20); // 添加小的延迟，创建交错效果
            } catch (error) {
                console.error(`创建截图容器 ${file.name} 失败:`, error);
            }
        }

        // 批量加载图片
        for (let batchIndex = 0; batchIndex < batches; batchIndex++) {
            // 延迟加载每一批，避免一次性加载所有图片
            setTimeout(async () => {
                // 找到属于当前批次的所有容器
                const batchContainers = Array.from(document.querySelectorAll(`.screenshot-item[data-batch-index="${batchIndex}"]`)) as HTMLElement[];

                // 并行加载当前批次的所有图片
                await Promise.all(batchContainers.map(async (container) => {
                    const filename = container.dataset.filename;
                    if (!filename || container.dataset.loaded === 'loaded') return;

                    try {
                        // 标记为正在加载
                        container.dataset.loaded = 'loading';

                        // 获取完整文件路径
                        const fullPath = await join(getPath(), filename);

                        // 直接读取图片数据，不经过压缩处理
                        const imageData = await readImageAsDataURL(fullPath);

                        if (imageData) {
                            const img = container.querySelector('img');
                            if (img) {
                                img.onload = () => {
                                    container.dataset.loaded = 'loaded';

                                    // 如果是第一个加载完成的图片并且没有当前选中的截图，自动选中它
                                    if (!firstLoadedItem) {
                                        firstLoadedItem = container;

                                        // 如果没有当前选中的截图，设置当前图片为选中状态
                                        if (!currentSelectedScreenshot) {
                                            currentSelectedScreenshot = filename;
                                            updateScreenshotSelection(container, img.src);
                                        }
                                    }

                                    // 如果这是当前选中的截图，显示预览图
                                    if (currentSelectedScreenshot === filename) {
                                        updateScreenshotSelection(container, img.src);
                                    }
                                };
                                img.src = imageData;
                            }
                        }
                    } catch (error) {
                        console.error(`加载图片 ${filename} 失败:`, error);
                        container.dataset.loaded = 'error';
                    }
                }));

                // 如果是最后一批且没有找到任何选中的图片，选择第一个
                if (batchIndex === batches - 1) {
                    setTimeout(() => {
                        if (!document.querySelector('.screenshot-item.active') && firstLoadedItem) {
                            const img = firstLoadedItem.querySelector('img') as HTMLImageElement;
                            if (img && img.src && !img.src.includes('data:image/svg+xml')) {
                                const filename = firstLoadedItem.dataset.filename;
                                if (filename) {
                                    currentSelectedScreenshot = filename;
                                    updateScreenshotSelection(firstLoadedItem, img.src);
                                }
                            }
                        }
                    }, 300);
                }
            }, batchIndex * 100); // 每100ms加载一批
        }
    } catch (error) {
        console.error('渲染截图失败:', error);
        messageError(`渲染截图失败: ${error}`);
    }
}

/**
 * 智能设备检查和选择
 * @returns 如果设备可用返回true，否则返回false
 */
async function checkAndSelectDevice(): Promise<boolean> {
    try {
        const { getDevices } = await import('../commands');
        const devices = await getDevices();

        // 过滤出在线设备
        const onlineDevices = devices.filter(device =>
            device.status === 'device'
        );

        if (onlineDevices.length === 0) {
            messageError('未检测到连接的设备，请确保设备已连接并启用USB调试');
            return false;
        }

        // 由于random_color_theme函数已经处理了设备选择，这里只需要验证设备可用性
        console.log(`检测到 ${onlineDevices.length} 个可用设备`);
        return true;

    } catch (error) {
        console.error('设备检查失败:', error);
        messageError(`设备检查失败: ${error}`);
        return false;
    }
}





/**
 * 使用shell命令复制图片到剪贴板（macOS）
 */
async function copyImageWithShellCommand(fullPath: string): Promise<boolean> {
    try {
        const startTime = performance.now();

        // 使用shellx插件执行osascript命令
        const result = await invoke('plugin:shellx|execute', {
            program: 'osascript',
            args: [
                '-e',
                `set the clipboard to (read (POSIX file "${fullPath}") as JPEG picture)`
            ],
            options: {}
        });

        const endTime = performance.now();
        console.log(`Shell命令复制耗时: ${(endTime - startTime).toFixed(2)}ms`);
        console.log('Shell命令执行结果:', result);

        return true;
    } catch (error) {
        console.error('Shell命令复制失败:', error);
        return false;
    }
}

/**
 * 复制选中的图片到剪贴板
 */
async function copySelectedImageToClipboard(): Promise<void> {
    try {
        // 检查是否有选中的截图
        if (!currentSelectedScreenshot) {
            messageError('请先选择一张图片');
            return;
        }

        // 获取图片文件路径
        const fullPath = await join(getPath(), currentSelectedScreenshot);

        // 检查文件是否存在
        if (!await exists(fullPath)) {
            messageError('图片文件不存在');
            return;
        }

        // 首先尝试使用shell命令复制（推荐方法）
        const shellCopySuccess = await copyImageWithShellCommand(fullPath);
        if (shellCopySuccess) {
            messageSuccess(`图片 ${currentSelectedScreenshot} 已复制到剪贴板`);
            console.log('图片复制成功（Shell命令）:', currentSelectedScreenshot);
            return;
        }

        console.log('Shell命令复制失败，回退到标准方法');

        // 回退到标准方法
        const startTime = performance.now();

        // 直接读取图片文件字节
        const imageBytes = await readFile(fullPath);
        const readTime = performance.now();

        console.log(`文件大小: ${imageBytes.length} 字节, 读取耗时: ${(readTime - startTime).toFixed(2)}ms`);

        // 复制到剪贴板
        const writeStart = performance.now();
        await writeImage(imageBytes);
        const endTime = performance.now();

        // 输出性能信息
        console.log(`图片复制性能: 读取耗时 ${(readTime - startTime).toFixed(2)}ms, 写入耗时 ${(endTime - writeStart).toFixed(2)}ms, 总耗时 ${(endTime - startTime).toFixed(2)}ms`);

        messageSuccess(`图片 ${currentSelectedScreenshot} 已复制到剪贴板`);
        console.log('图片复制成功:', currentSelectedScreenshot);
    } catch (error) {
        console.error('复制图片到剪贴板失败:', error);
        messageError(`复制失败: ${error}`);
    }
}

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', async () => {
    // 确保当前页面是颜色搜索页面，避免在主窗口中显示这些按钮
    if (!document.querySelector('.color-search-window')) {
        return; // 如果不是颜色搜索页面，退出初始化
    }

    // 确保初始化
    await init();

    // 加载模块状态配置
    console.log('开始加载模块状态配置...');
    await loadModuleStatusFromConfig();
    console.log('模块状态配置加载完成，当前状态:', moduleStatusModifications);

    // 渲染截图
    await renderScreenshots();

    // 给截图容器添加点击事件
    const container = document.getElementById('screenshots-container');
    if (container) {
        // 直接添加新的点击事件处理器
        container.addEventListener('click', (e) => {
            try {
                const target = e.target as HTMLElement;
                if (!target) return;

                const imgContainer = target.closest('.screenshot-img-container');
                if (!imgContainer) return;

                const screenshotItem = target.closest('.screenshot-item');
                if (!screenshotItem) return;

                const filename = screenshotItem.getAttribute('data-filename');
                if (!filename) return;

                const img = imgContainer.querySelector('.screenshot-img') as HTMLImageElement;
                if (!img || !img.src || img.src.includes('data:image/svg+xml')) return;

                // 更新当前选中的截图
                currentSelectedScreenshot = filename;

                // 更新UI状态
                updateScreenshotSelection(screenshotItem, img.src);

                console.log('截图点击处理成功');
            } catch (error) {
                console.error('截图点击处理失败:', error);
            }
        });

        console.log('截图容器点击事件设置成功');
    }

    // 创建按钮容器
    const guideContainer = document.createElement('div');
    guideContainer.className = 'guide-container';
    document.body.appendChild(guideContainer);

    // 创建添加截图按钮（纯文字版）
    const guide = document.createElement('div');
    guide.id = 'guide';
    guide.className = 'guide';
    guide.textContent = '点击对应颜色可获取资源名，按下 CTRL 可放大取色区域。';
    guideContainer.appendChild(guide);

    // 创建主按钮容器（统一容器，从右到左排列）
    const mainButtonsContainer = document.createElement('div');
    mainButtonsContainer.className = 'main-buttons-container';
    document.body.appendChild(mainButtonsContainer);

    // 创建添加截图按钮（纯文字版） - 最右侧
    const addScreenshotBtn = document.createElement('button');
    addScreenshotBtn.id = 'add-screenshot-button';
    addScreenshotBtn.className = 'add-screenshot-button';
    addScreenshotBtn.textContent = '添加截图';
    mainButtonsContainer.appendChild(addScreenshotBtn);

    // 创建清空所有截图按钮（纯文字版）
    const clearAllBtn = document.createElement('button');
    clearAllBtn.id = 'clear-all-button';
    clearAllBtn.className = 'clear-all-button';
    clearAllBtn.textContent = '清空截图';
    mainButtonsContainer.appendChild(clearAllBtn);

    // 创建生成随机颜色按钮（下拉版）
    const generateThemeBtn = createGenerateThemeButton();
    mainButtonsContainer.appendChild(generateThemeBtn);

    // 创建应用完整主题包按钮
    const applyFullBtn = createApplyThemeButton('混合版', 'full');
    mainButtonsContainer.appendChild(applyFullBtn);

    // 创建应用图片版本按钮
    const applyImageBtn = createApplyThemeButton('图片版', 'image');
    mainButtonsContainer.appendChild(applyImageBtn);

    // 创建应用颜色值版本按钮 - 最左侧
    const applyColorBtn = createApplyThemeButton('颜色版', 'color');
    mainButtonsContainer.appendChild(applyColorBtn);

    // 创建打开随机颜色主题目录按钮
    const openThemeFolderBtn = document.createElement('button');
    openThemeFolderBtn.className = 'add-screenshot-button';
    openThemeFolderBtn.textContent = '打开目录';
    openThemeFolderBtn.id = 'open-random-theme-folder-btn';
    mainButtonsContainer.appendChild(openThemeFolderBtn);

    openThemeFolderBtn.addEventListener('click', async () => {
        try {
            messageLoading('正在获取主题目录...');
            const rootDir = await get_the_root_directory_path('random_color_theme');
            const themePath = await join(rootDir, '随机颜色主题');
            await invoke('打开目录或文件', { path: themePath });
            messageSuccess('已请求打开目录');
        } catch (error) {
            console.error('打开随机颜色主题目录失败:', error);
            messageError(`打开目录失败: ${error}`);
        }
    });

    // 绑定添加截图按钮事件
    if (addScreenshotBtn) {
        addScreenshotBtn.addEventListener('click', async () => {
            try {
                // 调用截图功能
                await addScreenshot();
            } catch (error) {
                console.error('添加截图失败:', error);
                messageError('添加截图失败');
            }
        });
    } else {
        console.warn('未找到添加截图按钮');
    }

    // 绑定清空所有截图按钮事件
    if (clearAllBtn) {
        clearAllBtn.addEventListener('click', async () => {
            try {
                // 调用清空所有截图功能
                await clearAllScreenshots();
            } catch (error) {
                console.error('清空所有截图失败:', error);
                messageError('清空所有截图失败');
            }
        });
    } else {
        console.warn('未找到清空所有截图按钮');
    }

    // 添加键盘事件监听器处理图片复制
    document.addEventListener('keydown', async (event: KeyboardEvent) => {
        // 检查是否按下了Ctrl+C (Windows/Linux) 或 Command+C (macOS)
        if ((event.ctrlKey || event.metaKey) && event.key === 'c') {
            // 检查当前焦点是否在搜索框中
            const activeElement = document.activeElement;
            const isSearchInputFocused = activeElement &&
                (activeElement.id === 'color-range-search-input' ||
                    activeElement.tagName === 'INPUT' ||
                    activeElement.tagName === 'TEXTAREA');

            // 如果焦点在输入框中，不拦截复制操作，让浏览器处理文本复制
            if (isSearchInputFocused) {
                console.log('焦点在输入框中，允许复制文本内容');
                return;
            }

            // 检查是否有选中的截图
            if (currentSelectedScreenshot) {
                // 阻止默认的复制行为
                event.preventDefault();
                event.stopPropagation();

                // 复制选中的图片到剪贴板
                await copySelectedImageToClipboard();
            } else {
                // 如果没有选中图片，给用户提示
                console.log('没有选中的图片，跳过复制操作');
            }
        }
    });

    console.log('键盘事件监听器已设置，支持Ctrl+C/Command+C复制选中图片');
});

// 页面卸载前保存模块状态配置
window.addEventListener('beforeunload', async () => {
    if (hasModifications) {
        await saveModuleStatusToConfig();
    }
});

// 页面隐藏时也保存（例如切换应用时）
document.addEventListener('visibilitychange', async () => {
    if (document.visibilityState === 'hidden' && hasModifications) {
        await saveModuleStatusToConfig();
    }
});

/**
 * 重新加载 data.json 并刷新页面颜色数据
 */
export async function reloadDataJsonAndRefresh(): Promise<void> {
    try {
        // 强制重新加载颜色数据库，确保获取最新的颜色数据
        const { initColorDatabase } = await import('./color_database');
        await initColorDatabase(true); // 强制重新加载

        // 重新加载模块状态配置
        await loadModuleStatusFromConfig();
        // 重新渲染截图和颜色数据
        await renderScreenshots();
        // 如有其他颜色数据渲染函数，可在此补充调用
        // TODO: 若有更细粒度的颜色刷新逻辑，可补充
        console.log('data.json 已重新加载并刷新页面颜色数据');
    } catch (error) {
        console.error('重新加载 data.json 并刷新页面颜色数据失败:', error);
    }
}