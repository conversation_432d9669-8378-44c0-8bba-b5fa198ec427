<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TT 命令进度测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .progress-display {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            min-height: 100px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            white-space: pre-wrap;
            overflow-y: auto;
            max-height: 300px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .status.loading {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .status.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .status.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>TT 命令进度显示测试</h1>
        
        <div>
            <h3>测试批量操作进度显示</h3>
            <p>这个测试页面用于验证 tt 命令的实时进度显示功能。</p>
            
            <div>
                <label for="nameList">名称列表（逗号分隔）:</label><br>
                <input type="text" id="nameList" value="color1,color2,color3,color4,color5" style="width: 100%; padding: 8px; margin: 5px 0;">
            </div>
            
            <div>
                <label for="firstInterval">首次间隔（秒）:</label>
                <input type="number" id="firstInterval" value="2" style="width: 80px; padding: 5px;">
                
                <label for="otherInterval" style="margin-left: 20px;">其他间隔（秒）:</label>
                <input type="number" id="otherInterval" value="1" style="width: 80px; padding: 5px;">
            </div>
            
            <div style="margin: 15px 0;">
                <button id="startBtn">开始批量操作</button>
                <button id="stopBtn" disabled>停止操作</button>
            </div>
        </div>
        
        <div id="status" class="status" style="display: none;"></div>
        
        <div>
            <h4>实时进度输出:</h4>
            <div id="progressDisplay" class="progress-display">等待开始...</div>
        </div>
        
        <div>
            <h4>最终结果:</h4>
            <div id="resultDisplay" class="progress-display">暂无结果</div>
        </div>
    </div>

    <script type="module">
        // 模拟导入（在实际项目中这些会从相应模块导入）
        // import { executeBatchOperation } from './src/script/random_color_theme/regex_processor.js';
        // import { AbortError } from './src/script/terminal_commands.js';
        
        let currentAbortController = null;
        
        const startBtn = document.getElementById('startBtn');
        const stopBtn = document.getElementById('stopBtn');
        const progressDisplay = document.getElementById('progressDisplay');
        const resultDisplay = document.getElementById('resultDisplay');
        const statusDiv = document.getElementById('status');
        
        function showStatus(message, type = 'loading') {
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
            statusDiv.style.display = 'block';
        }
        
        function hideStatus() {
            statusDiv.style.display = 'none';
        }
        
        function appendProgress(message) {
            const timestamp = new Date().toLocaleTimeString();
            progressDisplay.textContent += `[${timestamp}] ${message}\n`;
            progressDisplay.scrollTop = progressDisplay.scrollHeight;
        }
        
        function clearProgress() {
            progressDisplay.textContent = '开始执行...\n';
        }
        
        startBtn.addEventListener('click', async () => {
            const nameList = document.getElementById('nameList').value.trim();
            const firstInterval = parseInt(document.getElementById('firstInterval').value) || 2;
            const otherInterval = parseInt(document.getElementById('otherInterval').value) || 1;
            
            if (!nameList) {
                showStatus('请输入名称列表', 'error');
                return;
            }
            
            // 重置显示
            clearProgress();
            resultDisplay.textContent = '执行中...';
            
            // 创建中断控制器
            currentAbortController = new AbortController();
            
            // 更新按钮状态
            startBtn.disabled = true;
            stopBtn.disabled = false;
            
            showStatus('正在执行批量操作...', 'loading');
            
            try {
                appendProgress(`开始批量操作: ${nameList}`);
                appendProgress(`参数: 首次间隔=${firstInterval}秒, 其他间隔=${otherInterval}秒`);
                
                // 这里应该调用实际的 executeBatchOperation 函数
                // 由于这是测试页面，我们模拟进度输出
                await simulateBatchOperation(nameList, firstInterval, otherInterval, currentAbortController.signal);
                
                showStatus('批量操作执行成功！', 'success');
                resultDisplay.textContent = '操作完成！所有项目已处理。';
                
            } catch (error) {
                if (error.name === 'AbortError') {
                    showStatus('操作已中断', 'error');
                    appendProgress('操作被用户中断');
                    resultDisplay.textContent = '操作已中断';
                } else {
                    showStatus(`操作失败: ${error.message}`, 'error');
                    appendProgress(`错误: ${error.message}`);
                    resultDisplay.textContent = `错误: ${error.message}`;
                }
            } finally {
                // 恢复按钮状态
                startBtn.disabled = false;
                stopBtn.disabled = true;
                currentAbortController = null;
            }
        });
        
        stopBtn.addEventListener('click', () => {
            if (currentAbortController) {
                currentAbortController.abort();
                appendProgress('正在停止操作...');
            }
        });
        
        // 模拟批量操作（用于测试）
        async function simulateBatchOperation(nameList, firstInterval, otherInterval, signal) {
            const items = nameList.split(',').map(item => item.trim());
            const totalOperations = items.length + 1; // N+1 操作
            
            // 检查中断
            if (signal.aborted) {
                throw new Error('AbortError');
            }
            
            // 模拟首次批量操作
            appendProgress(`(1/${totalOperations}) 首次: 包含所有值`);
            await sleep(firstInterval * 1000, signal);
            
            // 模拟逐个操作
            for (let i = 0; i < items.length; i++) {
                if (signal.aborted) {
                    throw new Error('AbortError');
                }
                
                const currentOp = i + 2;
                appendProgress(`(${currentOp}/${totalOperations}) 完成: ${items[i]}`);
                
                if (i < items.length - 1) { // 不是最后一个
                    await sleep(otherInterval * 1000, signal);
                }
            }
        }
        
        // 支持中断的睡眠函数
        function sleep(ms, signal) {
            return new Promise((resolve, reject) => {
                const timeoutId = setTimeout(resolve, ms);
                
                const abortHandler = () => {
                    clearTimeout(timeoutId);
                    reject(new Error('AbortError'));
                };
                
                signal.addEventListener('abort', abortHandler);
                
                setTimeout(() => {
                    signal.removeEventListener('abort', abortHandler);
                }, ms);
            });
        }
    </script>
</body>
</html>
