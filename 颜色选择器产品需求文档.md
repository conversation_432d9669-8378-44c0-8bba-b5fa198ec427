# Mini Edit Pro 颜色选择器产品需求文档 (PRD)

## 1. 产品概述

### 1.1 项目背景
Mini Edit Pro 是一个基于 Tauri 框架开发的桌面应用程序，专门为小米主题开发者设计。该应用提供了一套完整的主题制作工具链，其中颜色选择器是核心功能之一，用于帮助开发者精确获取屏幕上的颜色值并关联到对应的主题资源。

### 1.2 核心功能
颜色选择器是一个高精度的屏幕取色工具，支持：
- 实时屏幕颜色拾取
- 放大镜预览功能
- 颜色值与主题资源的智能关联
- 快捷键操作支持
- 跨平台兼容性（Windows、macOS）

### 1.3 目标用户
- 小米主题开发者
- UI/UX 设计师
- 前端开发工程师
- 需要精确颜色匹配的设计人员

## 2. 功能需求

### 2.1 颜色选择器核心功能

#### 2.1.1 屏幕取色功能
- **功能描述**：点击屏幕任意位置获取该位置的颜色值
- **输出格式**：HEX 格式颜色值（如 #FF0000）
- **精度要求**：像素级精确取色
- **响应时间**：< 10ms

#### 2.1.2 放大镜预览
- **预览尺寸**：200x200 像素预览区域
- **放大倍数**：2倍预览图尺寸
- **网格显示**：17x17 像素网格，便于精确定位
- **实时跟随**：鼠标移动时实时更新预览内容
- **双层渲染**：
  - 底层：放大预览图
  - 顶层：原始尺寸图（通过圆形遮罩显示）

#### 2.1.3 颜色信息显示
- **颜色值显示**：实时显示当前鼠标位置的颜色值
- **坐标显示**：显示当前鼠标的屏幕坐标
- **资源关联**：如果颜色在数据库中存在，显示对应的资源名称
- **动态更新**：鼠标移动时实时更新所有信息

### 2.2 用户交互方式

#### 2.2.1 激活方式
- **自动激活**：鼠标移动到预览图所在div自动激活取色器

#### 2.2.2 操作方式
- **鼠标点击**：点击目标位置完成取色
- **键盘控制**：
  - ESC 键：取消取色操作
  - 方向键：微调鼠标位置（精确到像素）
- **鼠标移动**：实时预览和颜色信息更新

#### 2.2.3 退出机制
- **ESC 键退出**：全局 ESC 键监听，确保任何情况下都能退出
- **鼠标离开**：鼠标离开窗口区域自动退出
- **区域限制**：鼠标离开预览区域自动退出

### 2.3 数据处理和存储机制

#### 2.3.1 颜色数据库
- **数据结构**：
  ```typescript
  interface ColorDatabase {
    [colorHex: string]: {
      name: string;    // 资源名称
      target: string;  // 目标路径
    }
  }
  ```
- **存储位置**：`随机颜色主题/data.json`
- **加载机制**：1.页面加载时自动加载。2.生成随机颜色主题成功时加载
- **缓存策略**：内存缓存，避免重复文件读取

#### 2.3.2 数据同步
- **实时查询**：取色时实时查询颜色数据库
- **缓存优化**：相同颜色值使用缓存结果
- **错误处理**：数据库不存在时提供友好提示

### 2.4 剪贴板集成
- **自动复制**：取色成功后自动复制到系统剪贴板
- **智能复制**：
  - 如果颜色在数据库中存在，复制资源名称
  - 如果颜色不存在，复制颜色值
- **复制反馈**：通过消息提示确认复制成功

## 3. 技术实现

### 3.1 技术栈
- **前端框架**：Vanilla TypeScript + HTML5 Canvas
- **桌面框架**：Tauri 2.x
- **后端语言**：Rust
- **构建工具**：Vite
- **包管理**：Yarn

### 3.2 核心技术实现

#### 3.2.1 屏幕截图技术
- **Canvas API**：使用 HTML5 Canvas 进行像素级操作
- **ImageData 处理**：直接操作像素数据获取颜色值
- **跨平台兼容**：通过 Tauri 插件实现跨平台屏幕访问

#### 3.2.2 事件处理机制
- **全局事件监听**：document 级别的事件监听
- **事件防抖**：避免频繁的鼠标移动事件处理
- **事件优先级**：ESC 键具有最高优先级

#### 3.2.3 性能优化
- **帧率控制**：限制预览更新频率，避免性能问题
- **内存管理**：及时清理 Canvas 和事件监听器
- **异步处理**：文件读取和数据库查询使用异步操作

### 3.3 架构设计
- **模块化设计**：功能拆分为独立模块
- **状态管理**：全局状态管理，避免状态冲突
- **错误处理**：完善的错误处理和恢复机制
- **平台适配**：针对不同操作系统的特殊处理

## 4. 用户体验

### 4.1 界面设计
- **简洁直观**：最小化界面元素，专注于取色功能
- **实时反馈**：所有操作都有即时的视觉反馈
- **无干扰设计**：取色时隐藏不必要的界面元素

### 4.2 操作流程
1. **激活取色器**：点击按钮或使用快捷键
2. **移动鼠标**：在屏幕上移动查看预览
3. **精确定位**：使用放大镜精确定位目标像素
4. **确认取色**：点击鼠标完成取色
5. **获取结果**：颜色值自动复制到剪贴板

### 4.3 用户引导
- **操作提示**：首次使用时提供操作指导
- **快捷键提示**：界面显示可用的快捷键
- **状态指示**：清晰的状态指示器显示当前操作状态

## 5. 功能边界

### 5.1 当前版本支持的功能
- ✅ 基础屏幕取色功能
- ✅ 放大镜预览
- ✅ 颜色数据库关联
- ✅ 剪贴板自动复制
- ✅ 快捷键支持
- ✅ 跨平台兼容
- ✅ 错误处理和恢复
- ✅ 性能优化

### 5.2 当前版本不支持的功能
- ❌ 颜色历史记录
- ❌ 自定义颜色格式（RGB、HSL等）
- ❌ 批量取色功能
- ❌ 颜色调色板生成
- ❌ 颜色对比度分析
- ❌ 多显示器支持优化
- ❌ 取色区域选择
- ❌ 颜色匹配算法

### 5.3 技术限制
- **浏览器安全限制**：受浏览器安全策略限制，某些功能需要 Tauri 支持
- **系统权限**：需要屏幕访问权限
- **性能限制**：大尺寸屏幕可能影响性能
- **平台差异**：不同操作系统的行为可能略有差异

## 6. 质量保证

### 6.1 测试策略
- **功能测试**：覆盖所有核心功能点
- **兼容性测试**：多平台、多分辨率测试
- **性能测试**：内存使用和响应时间测试
- **用户体验测试**：真实用户场景测试

### 6.2 错误处理
- **优雅降级**：功能不可用时提供替代方案
- **错误恢复**：自动恢复机制，避免程序崩溃
- **用户反馈**：清晰的错误信息和解决建议

---

**文档版本**：v1.0
**最后更新**：2025-01-10
**负责人**：开发团队
**审核状态**：待审核
