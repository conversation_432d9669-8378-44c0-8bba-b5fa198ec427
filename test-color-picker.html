<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>屏幕取色器测试</title>
    <link rel="stylesheet" href="./src/script/random_color_theme/color-picker.css">
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', sans-serif;
            background: #f5f5f7;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }
        
        .test-title {
            font-size: 24px;
            font-weight: 600;
            color: #1d1d1f;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .test-description {
            color: #666;
            margin-bottom: 30px;
            line-height: 1.6;
        }
        
        .preview-container {
            position: relative;
            width: 100%;
            height: 400px;
            border: 2px solid #e5e5e7;
            border-radius: 8px;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #fafafa;
        }
        
        .test-image {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
            border-radius: 6px;
        }
        
        .instructions {
            margin-top: 20px;
            padding: 16px;
            background: #f0f8ff;
            border-radius: 8px;
            border-left: 4px solid #007AFF;
        }
        
        .instructions h3 {
            margin: 0 0 10px 0;
            color: #007AFF;
            font-size: 16px;
        }
        
        .instructions ul {
            margin: 0;
            padding-left: 20px;
        }
        
        .instructions li {
            margin-bottom: 5px;
            color: #333;
        }
        
        .color-info-display {
            margin-top: 20px;
            padding: 16px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }
        
        .color-info-display h3 {
            margin: 0 0 10px 0;
            color: #333;
            font-size: 16px;
        }
        
        .color-sample {
            display: inline-block;
            width: 40px;
            height: 40px;
            border-radius: 6px;
            border: 2px solid #ddd;
            margin-right: 15px;
            vertical-align: middle;
        }
        
        .color-values {
            display: inline-block;
            vertical-align: middle;
            font-family: 'JetBrains Mono', monospace;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">屏幕取色器功能测试</h1>
        
        <div class="test-description">
            这是一个用于测试屏幕取色器功能的演示页面。将鼠标移入下方的图片区域，即可体验取色器的放大镜和颜色信息显示功能。
        </div>
        
        <div class="preview-container" id="testPreviewContainer">
            <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8ZGVmcz4KICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iZ3JhZGllbnQiIHgxPSIwJSIgeTE9IjAlIiB4Mj0iMTAwJSIgeTI9IjEwMCUiPgogICAgICA8c3RvcCBvZmZzZXQ9IjAlIiBzdHlsZT0ic3RvcC1jb2xvcjojRkY2B0I0O3N0b3Atb3BhY2l0eToxIiAvPgogICAgICA8c3RvcCBvZmZzZXQ9IjMzJSIgc3R5bGU9InN0b3AtY29sb3I6IzAwN0FGRjtzdG9wLW9wYWNpdHk6MSIgLz4KICAgICAgPHN0b3Agb2Zmc2V0PSI2NiUiIHN0eWxlPSJzdG9wLWNvbG9yOiMzNEM3NTk7c3RvcC1vcGFjaXR5OjEiIC8+CiAgICAgIDxzdG9wIG9mZnNldD0iMTAwJSIgc3R5bGU9InN0b3AtY29sb3I6I0ZGRDYwQTtzdG9wLW9wYWNpdHk6MSIgLz4KICAgIDwvbGluZWFyR3JhZGllbnQ+CiAgPC9kZWZzPgogIDxyZWN0IHdpZHRoPSI0MDAiIGhlaWdodD0iMzAwIiBmaWxsPSJ1cmwoI2dyYWRpZW50KSIvPgogIDx0ZXh0IHg9IjIwMCIgeT0iMTUwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSJ3aGl0ZSIgZm9udC1mYW1pbHk9IkFyaWFsLCBzYW5zLXNlcmlmIiBmb250LXNpemU9IjI0IiBmb250LXdlaWdodD0iYm9sZCI+5Y+W6Imy5Zmo5rWL6K+VPC90ZXh0Pgo8L3N2Zz4K" 
                 alt="测试图片" 
                 class="test-image" 
                 id="testImage">
        </div>
        
        <div class="instructions">
            <h3>使用说明：</h3>
            <ul>
                <li>将鼠标移入上方图片区域，取色器会自动激活</li>
                <li>鼠标指针会变为不可见，同时显示放大镜</li>
                <li>移动鼠标查看不同位置的颜色信息</li>
                <li>放大镜中心的小方框指示当前选中的像素</li>
                <li>颜色信息会显示在放大镜上方</li>
                <li>按ESC键或移出图片区域退出取色模式</li>
            </ul>
        </div>
        
        <div class="color-info-display">
            <h3>当前颜色信息：</h3>
            <div id="colorDisplay">
                <span class="color-sample" style="background-color: #ccc;"></span>
                <span class="color-values">将鼠标移入图片区域开始取色</span>
            </div>
        </div>
    </div>

    <script type="module">
        // 模拟取色器功能的简化版本
        // 注意：这只是一个演示，实际的取色器功能在完整的应用中实现
        
        const container = document.getElementById('testPreviewContainer');
        const image = document.getElementById('testImage');
        const colorDisplay = document.getElementById('colorDisplay');
        
        // 简单的演示功能
        container.addEventListener('mouseenter', () => {
            console.log('取色器激活');
            container.style.cursor = 'none';
        });
        
        container.addEventListener('mouseleave', () => {
            console.log('取色器停用');
            container.style.cursor = '';
        });
        
        container.addEventListener('mousemove', (e) => {
            const rect = container.getBoundingClientRect();
            const x = Math.round(e.clientX - rect.left);
            const y = Math.round(e.clientY - rect.top);
            
            // 模拟颜色信息显示
            const colorSample = colorDisplay.querySelector('.color-sample');
            const colorValues = colorDisplay.querySelector('.color-values');
            
            // 生成随机颜色作为演示
            const r = Math.floor(Math.random() * 256);
            const g = Math.floor(Math.random() * 256);
            const b = Math.floor(Math.random() * 256);
            const hex = `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
            
            colorSample.style.backgroundColor = hex;
            colorValues.textContent = `RGB: ${r}, ${g}, ${b} | HEX: ${hex.toUpperCase()} | 坐标: (${x}, ${y})`;
        });
        
        // ESC键退出
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                container.style.cursor = '';
                console.log('ESC键退出取色模式');
            }
        });
    </script>
</body>
</html>
