<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>复制 TT 命令功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }

        .test-section {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
        }

        .output-area {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            min-height: 100px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            white-space: pre-wrap;
            overflow-y: auto;
            max-height: 300px;
        }

        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }

        button:hover {
            background: #0056b3;
        }

        button.copy-btn {
            background: #28a745;
        }

        button.copy-btn:hover {
            background: #218838;
        }

        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            color: #155724;
        }

        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            color: #721c24;
        }

        .command-display {
            background: #e9ecef;
            border: 1px solid #ced4da;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            word-break: break-all;
        }

        input[type="text"] {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

        .highlight {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>

<body>
    <div class="container">
        <h1>📋 复制 TT 命令功能测试</h1>
        <div class="highlight">
            <strong>功能说明：</strong>现在执行按钮已改为复制按钮，点击后会静默地将对应的 tt 命令复制到剪贴板，不显示任何消息提示，用户可以自己决定何时执行。
        </div>
    </div>

    <div class="container">
        <h2>🧪 复制功能测试</h2>
        <div class="test-section">
            <h3>1. 基础复制功能测试</h3>
            <p>测试复制文本到剪贴板的基础功能</p>

            <div>
                <label>测试文本：</label>
                <input type="text" id="testText" value='tt "primary_color,secondary_color,accent_color" 10 2'
                    placeholder="输入要复制的文本">
            </div>

            <button class="copy-btn" onclick="testBasicCopy()">复制测试文本</button>
            <button onclick="pasteFromClipboard()">粘贴剪贴板内容</button>
            <button onclick="clearOutput('basicOutput')">清除输出</button>

            <div id="basicOutput" class="output-area">等待测试...</div>
        </div>

        <div class="test-section">
            <h3>2. TT 命令生成测试</h3>
            <p>测试根据项目列表生成 TT 命令</p>

            <div>
                <label>项目列表（逗号分隔）：</label>
                <input type="text" id="projectList"
                    value="primary_color,secondary_color,accent_color,background_color,text_color"
                    placeholder="输入项目名称，用逗号分隔">
            </div>

            <div>
                <label>首次间隔（秒）：</label>
                <input type="number" id="firstInterval" value="10" min="0" max="60" style="width: 80px;">

                <label style="margin-left: 20px;">其他间隔（秒）：</label>
                <input type="number" id="otherInterval" value="2" min="0" max="30" style="width: 80px;">
            </div>

            <button class="copy-btn" onclick="generateAndCopyCommand()">生成并复制 TT 命令</button>
            <button onclick="clearOutput('commandOutput')">清除输出</button>

            <div id="commandOutput" class="output-area">等待生成...</div>
        </div>
    </div>

    <div class="container">
        <h2>🎯 模拟实际场景</h2>
        <div class="test-section">
            <h3>3. 模拟颜色搜索结果</h3>
            <p>模拟在颜色搜索结果中点击复制按钮的场景</p>

            <div class="highlight">
                <strong>场景说明：</strong>模拟用户在颜色搜索结果中找到了一些颜色值，然后点击"复制颜色命令"按钮的情况。
            </div>

            <button class="copy-btn" onclick="simulateColorCopy()">模拟复制颜色命令 (5个)</button>
            <button class="copy-btn" onclick="simulateImageCopy()">模拟复制图片命令 (3个)</button>
            <button onclick="clearOutput('scenarioOutput')">清除输出</button>

            <div id="scenarioOutput" class="output-area">等待模拟...</div>
        </div>
    </div>

    <div class="container">
        <h2>📊 剪贴板状态</h2>
        <div class="test-section">
            <div id="clipboardStatus" class="output-area">点击"检查剪贴板"按钮查看当前剪贴板内容</div>
            <button onclick="checkClipboard()">检查剪贴板</button>
            <button onclick="clearClipboard()">清空剪贴板</button>
        </div>
    </div>

    <script>
        // 复制到剪贴板的工具函数
        async function copyToClipboard(text) {
            try {
                if (navigator.clipboard && window.isSecureContext) {
                    await navigator.clipboard.writeText(text);
                    return true;
                } else {
                    // 降级到传统方法
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    textArea.style.position = 'fixed';
                    textArea.style.left = '-999999px';
                    textArea.style.top = '-999999px';
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();

                    const successful = document.execCommand('copy');
                    document.body.removeChild(textArea);
                    return successful;
                }
            } catch (error) {
                console.error('复制到剪贴板失败:', error);
                return false;
            }
        }

        // 从剪贴板读取内容
        async function readFromClipboard() {
            try {
                if (navigator.clipboard && window.isSecureContext) {
                    return await navigator.clipboard.readText();
                } else {
                    // 传统方法无法读取剪贴板，返回提示
                    return '[无法读取剪贴板内容 - 请手动粘贴]';
                }
            } catch (error) {
                console.error('读取剪贴板失败:', error);
                return '[读取失败]';
            }
        }

        // 测试函数
        window.testBasicCopy = async function () {
            const text = document.getElementById('testText').value;
            const output = document.getElementById('basicOutput');

            output.textContent = '正在复制...\n';

            const success = await copyToClipboard(text);

            if (success) {
                output.textContent += `✅ 复制成功！\n复制的内容: ${text}\n\n`;
                output.textContent += '现在可以在任何地方按 Ctrl+V (或 Cmd+V) 粘贴。\n';
            } else {
                output.textContent += `❌ 复制失败\n尝试复制的内容: ${text}\n`;
            }
        };

        window.pasteFromClipboard = async function () {
            const output = document.getElementById('basicOutput');
            const clipboardContent = await readFromClipboard();

            output.textContent += `\n📋 剪贴板内容: ${clipboardContent}\n`;
        };

        window.generateAndCopyCommand = async function () {
            const projectList = document.getElementById('projectList').value.trim();
            const firstInterval = document.getElementById('firstInterval').value;
            const otherInterval = document.getElementById('otherInterval').value;
            const output = document.getElementById('commandOutput');

            if (!projectList) {
                output.textContent = '❌ 请输入项目列表\n';
                return;
            }

            // 生成 TT 命令
            const command = `tt "${projectList}" ${firstInterval} ${otherInterval}`;

            output.textContent = `生成的 TT 命令:\n`;
            output.textContent += `${command}\n\n`;

            // 复制到剪贴板
            const success = await copyToClipboard(command);

            if (success) {
                output.textContent += `✅ 命令已复制到剪贴板！\n`;
                output.textContent += `现在可以在终端中粘贴并执行此命令。\n`;
            } else {
                output.textContent += `❌ 复制失败，请手动复制上面的命令\n`;
            }
        };

        window.simulateColorCopy = async function () {
            const output = document.getElementById('scenarioOutput');

            // 模拟颜色搜索结果
            const mockColors = [
                'primary_color',
                'secondary_color',
                'accent_color',
                'background_color',
                'text_color'
            ];

            const nameList = mockColors.join(',');
            const command = `tt "${nameList}" 10 2`;

            output.textContent = `模拟颜色复制操作:\n`;
            output.textContent += `目标: system_ui\n`;
            output.textContent += `类型: 颜色值\n`;
            output.textContent += `项目数量: ${mockColors.length}个\n`;
            output.textContent += `生成的命令: ${command}\n\n`;

            const success = await copyToClipboard(command);

            if (success) {
                output.textContent += `✅ 命令已静默复制到剪贴板（无消息提示）\n`;
                output.textContent += `命令: ${command}\n`;
            } else {
                output.textContent += `❌ 复制失败\n`;
            }
        };

        window.simulateImageCopy = async function () {
            const output = document.getElementById('scenarioOutput');

            // 模拟图片搜索结果
            const mockImages = [
                'icon_back.png',
                'icon_home.9.png',
                'background_image.png'
            ];

            const nameList = mockImages.join(',');
            const command = `tt "${nameList}" 10 2`;

            output.textContent += `\n模拟图片复制操作:\n`;
            output.textContent += `目标: launcher\n`;
            output.textContent += `类型: 图片\n`;
            output.textContent += `项目数量: ${mockImages.length}个\n`;
            output.textContent += `生成的命令: ${command}\n\n`;

            const success = await copyToClipboard(command);

            if (success) {
                output.textContent += `✅ 命令已静默复制到剪贴板（无消息提示）\n`;
                output.textContent += `命令: ${command}\n`;
            } else {
                output.textContent += `❌ 复制失败\n`;
            }
        };

        window.checkClipboard = async function () {
            const statusDiv = document.getElementById('clipboardStatus');
            const content = await readFromClipboard();

            statusDiv.innerHTML = `
当前剪贴板内容:
━━━━━━━━━━━━━━━━━━━━
${content}
━━━━━━━━━━━━━━━━━━━━
检查时间: ${new Date().toLocaleString()}
            `;
        };

        window.clearClipboard = async function () {
            await copyToClipboard('');
            const statusDiv = document.getElementById('clipboardStatus');
            statusDiv.textContent = '剪贴板已清空';
        };

        window.clearOutput = function (id) {
            document.getElementById(id).textContent = '已清除...';
        };

        // 页面加载完成后的初始化
        console.log('📋 复制 TT 命令功能测试页面已加载');
        console.log('请点击各个测试按钮来验证复制功能');
    </script>
</body>

</html>