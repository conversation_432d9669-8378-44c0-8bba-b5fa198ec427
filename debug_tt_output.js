// 调试 tt 命令输出的测试脚本
// 在浏览器控制台中运行此代码来测试 tt 命令的输出捕获

async function testTtOutput() {
    console.log('开始测试 tt 命令输出捕获...');
    
    try {
        // 导入必要的模块（需要在实际环境中运行）
        const { executeCommand } = await import('./src/script/terminal_commands.js');
        
        // 测试简单的 tt 命令
        const testCommand = 'tt "color1,color2,color3" 1 1';
        console.log('执行命令:', testCommand);
        
        let outputLines = [];
        
        const result = await executeCommand(
            testCommand,
            undefined, // 无中断信号
            (output) => {
                // 实时输出回调
                console.log('实时输出:', JSON.stringify(output));
                outputLines.push(output);
                
                // 检查是否包含进度信息
                if (output.includes('(') && output.includes('/') && output.includes(')')) {
                    console.log('🎯 发现进度信息:', output.trim());
                }
            }
        );
        
        console.log('命令执行完成');
        console.log('最终结果:', result);
        console.log('捕获的输出行数:', outputLines.length);
        console.log('所有输出:', outputLines);
        
    } catch (error) {
        console.error('测试失败:', error);
    }
}

// 简化版本的测试（不依赖模块导入）
async function testWithDirectCommand() {
    console.log('开始直接测试...');
    
    // 模拟 tt 命令的输出格式
    const mockTtOutput = [
        '数组模式: 3个项目，首次间隔1秒，其余间隔1秒',
        '(1/4) 首次: 包含所有值',
        '(2/4) 完成: color1',
        '(3/4) 完成: color2',
        '(4/4) 完成: color3'
    ];
    
    console.log('模拟 tt 命令输出:');
    mockTtOutput.forEach((line, index) => {
        setTimeout(() => {
            console.log(`输出行 ${index + 1}:`, line);
            
            // 检查进度信息
            const progressMatch = line.match(/\((\d+)\/(\d+)\)/);
            if (progressMatch) {
                const current = progressMatch[1];
                const total = progressMatch[2];
                console.log(`🎯 进度: ${current}/${total} (${Math.round(current/total*100)}%)`);
            }
        }, index * 500);
    });
}

// 检查当前页面是否有消息显示功能
function checkMessageSystem() {
    console.log('检查消息系统...');
    
    // 查找消息容器
    const messageContainer = document.getElementById('message-container');
    if (messageContainer) {
        console.log('✅ 找到消息容器:', messageContainer);
    } else {
        console.log('❌ 未找到消息容器');
    }
    
    // 检查是否有 messageLoading 函数
    if (typeof window.messageLoading === 'function') {
        console.log('✅ messageLoading 函数可用');
        
        // 测试消息显示
        window.messageLoading('测试进度显示: (1/5) 完成: test_item');
        
        setTimeout(() => {
            window.messageLoading('测试进度显示: (2/5) 完成: test_item2');
        }, 1000);
        
        setTimeout(() => {
            window.messageLoading('测试进度显示: (3/5) 完成: test_item3');
        }, 2000);
        
    } else {
        console.log('❌ messageLoading 函数不可用');
    }
}

// 运行测试
console.log('=== TT 命令输出调试工具 ===');
console.log('请在浏览器控制台中运行以下函数:');
console.log('1. testWithDirectCommand() - 测试模拟输出');
console.log('2. checkMessageSystem() - 检查消息系统');
console.log('3. testTtOutput() - 测试实际 tt 命令（需要在应用环境中）');

// 自动运行基础检查
checkMessageSystem();
testWithDirectCommand();
