<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分段复制功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }

        .test-section {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
        }

        .button-group {
            display: flex;
            flex-wrap: wrap;
            gap: 3px;
            margin: 15px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 4px;
            border: 1px solid #dee2e6;
            align-items: center;
        }

        .copy-btn {
            background: rgba(52, 199, 89, 0.9);
            color: white;
            border: 0.5px solid rgba(0, 0, 0, 0.08);
            padding: 0 8px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 9px;
            font-weight: 500;
            white-space: nowrap;
            height: 22px;
            min-width: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'Helvetica Neue', sans-serif;
            transition: all 0.15s ease-out;
            flex-shrink: 0;
        }

        .copy-btn:hover {
            background: rgba(52, 199, 89, 1);
            transform: translateY(-1px) scale(1.02);
        }

        .copy-btn.full {
            background: rgba(52, 199, 89, 1);
            font-weight: 600;
            min-width: 40px;
            border-width: 1px;
            border-color: rgba(52, 199, 89, 0.4);
        }

        .copy-btn.image {
            background: rgba(255, 149, 0, 0.9);
        }

        .copy-btn.image:hover {
            background: rgba(255, 149, 0, 1);
        }

        .copy-btn.image.full {
            background: rgba(255, 149, 0, 1);
            border-color: rgba(255, 149, 0, 0.4);
        }

        .output-area {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            min-height: 100px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            white-space: pre-wrap;
            overflow-y: auto;
            max-height: 300px;
        }

        .highlight {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }

        .stats {
            background: #e9ecef;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }

        input[type="number"] {
            width: 80px;
            padding: 5px;
            margin: 0 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }

        button:hover {
            background: #0056b3;
        }
    </style>
</head>

<body>
    <div class="container">
        <h1>📊 分段复制功能测试</h1>
        <div class="highlight">
            <strong>功能说明：</strong>根据项目总数量智能分段，创建多个复制按钮：
            <ul>
                <li><strong>≤4个</strong>：只有1个完整复制按钮</li>
                <li><strong>5-8个</strong>：1个完整 + 2个分段</li>
                <li><strong>9-16个</strong>：1个完整 + 4个分段</li>
                <li><strong>17-32个</strong>：1个完整 + 5个分段</li>
                <li><strong>33个以上</strong>：1个完整 + 8个分段</li>
            </ul>
        </div>
    </div>

    <div class="container">
        <h2>🎛️ 分段策略测试</h2>
        <div class="test-section">
            <h3>自定义数量测试</h3>
            <div>
                <label>项目数量：</label>
                <input type="number" id="customCount" value="20" min="1" max="100">
                <button onclick="testCustomCount()">生成分段按钮</button>
                <button onclick="clearOutput('customOutput')">清除</button>
            </div>

            <div id="customButtons" class="button-group">
                <!-- 动态生成的按钮将显示在这里 -->
            </div>

            <div id="customOutput" class="output-area">请输入数量并点击"生成分段按钮"</div>
        </div>
    </div>

    <div class="container">
        <h2>📋 预设场景测试</h2>

        <div class="test-section">
            <h3>场景1：少量项目 (3个颜色值)</h3>
            <div class="stats">总数：3个 | 按钮：[全部]</div>
            <div id="scenario1Buttons" class="button-group"></div>
            <div id="scenario1Output" class="output-area">等待测试...</div>
        </div>

        <div class="test-section">
            <h3>场景2：中等数量 (12个图片)</h3>
            <div class="stats">总数：12个 | 按钮：[全部] [1/4] [2/4] [3/4] [4/4]</div>
            <div id="scenario2Buttons" class="button-group"></div>
            <div id="scenario2Output" class="output-area">等待测试...</div>
        </div>

        <div class="test-section">
            <h3>场景3：较多项目 (25个颜色值)</h3>
            <div class="stats">总数：25个 | 按钮：[全部] [1/5] [2/5] [3/5] [4/5] [5/5]</div>
            <div id="scenario3Buttons" class="button-group"></div>
            <div id="scenario3Output" class="output-area">等待测试...</div>
        </div>

        <div class="test-section">
            <h3>场景4：大量项目 (50个图片)</h3>
            <div class="stats">总数：50个 | 按钮：[全部] [1/8] [2/8] ... [8/8]</div>
            <div id="scenario4Buttons" class="button-group"></div>
            <div id="scenario4Output" class="output-area">等待测试...</div>
        </div>
    </div>

    <div class="container">
        <h2>📊 剪贴板监控</h2>
        <div class="test-section">
            <div id="clipboardStatus" class="output-area">点击"检查剪贴板"查看当前内容</div>
            <button onclick="checkClipboard()">检查剪贴板</button>
            <button onclick="clearClipboard()">清空剪贴板</button>
        </div>
    </div>

    <script>
        // 模拟分段计算逻辑
        function calculateSegments(totalCount) {
            if (totalCount <= 4) {
                return { segmentCount: 0, itemsPerSegment: 0 }; // 只有完整按钮
            } else if (totalCount <= 8) {
                return { segmentCount: 2, itemsPerSegment: Math.ceil(totalCount / 2) };
            } else if (totalCount <= 16) {
                return { segmentCount: 4, itemsPerSegment: Math.ceil(totalCount / 4) };
            } else if (totalCount <= 32) {
                return { segmentCount: 5, itemsPerSegment: Math.ceil(totalCount / 5) };
            } else {
                return { segmentCount: 8, itemsPerSegment: Math.ceil(totalCount / 8) };
            }
        }

        // 生成模拟项目列表
        function generateMockItems(count, type) {
            const items = [];
            for (let i = 1; i <= count; i++) {
                if (type === 'color') {
                    items.push(`color_${i.toString().padStart(2, '0')}`);
                } else {
                    items.push(`image_${i.toString().padStart(2, '0')}.png`);
                }
            }
            return items;
        }

        // 复制到剪贴板
        async function copyToClipboard(text) {
            try {
                if (navigator.clipboard && window.isSecureContext) {
                    await navigator.clipboard.writeText(text);
                    return true;
                } else {
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    textArea.style.position = 'fixed';
                    textArea.style.left = '-999999px';
                    textArea.style.top = '-999999px';
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();

                    const successful = document.execCommand('copy');
                    document.body.removeChild(textArea);
                    return successful;
                }
            } catch (error) {
                console.error('复制失败:', error);
                return false;
            }
        }

        // 创建分段按钮组
        function createCopyButtonGroup(items, type, outputId) {
            const totalCount = items.length;
            const typeText = type === 'color' ? '颜色' : '图片';
            const segmentInfo = calculateSegments(totalCount);

            const buttons = [];

            // 创建完整复制按钮
            const fullButton = document.createElement('button');
            fullButton.className = `copy-btn full ${type}`;
            fullButton.textContent = `全部`;
            fullButton.onclick = () => handleCopy(items, `全部${typeText}`, outputId);
            buttons.push(fullButton);

            // 如果需要分段，创建分段按钮
            if (segmentInfo.segmentCount > 0) {
                for (let i = 0; i < segmentInfo.segmentCount; i++) {
                    const startIndex = i * segmentInfo.itemsPerSegment;
                    const endIndex = Math.min(startIndex + segmentInfo.itemsPerSegment, totalCount);
                    const segmentItems = items.slice(startIndex, endIndex);

                    const segmentButton = document.createElement('button');
                    segmentButton.className = `copy-btn ${type}`;
                    segmentButton.textContent = `${i + 1}/${segmentInfo.segmentCount}`;
                    segmentButton.onclick = () => handleCopy(segmentItems, `${typeText}分段${i + 1}`, outputId);
                    buttons.push(segmentButton);
                }
            }

            return buttons;
        }

        // 处理复制操作
        async function handleCopy(items, description, outputId) {
            const command = `tt "${items.join(',')}" 10 2`;
            const success = await copyToClipboard(command);

            const output = document.getElementById(outputId);
            const timestamp = new Date().toLocaleTimeString();

            output.textContent += `[${timestamp}] ${description}\n`;
            output.textContent += `项目数量: ${items.length}\n`;
            output.textContent += `命令: ${command}\n`;
            output.textContent += `复制状态: ${success ? '✅ 成功' : '❌ 失败'}\n\n`;
            output.scrollTop = output.scrollHeight;
        }

        // 测试函数
        window.testCustomCount = function () {
            const count = parseInt(document.getElementById('customCount').value);
            if (count < 1 || count > 100) {
                alert('请输入1-100之间的数字');
                return;
            }

            const items = generateMockItems(count, 'color');
            const buttons = createCopyButtonGroup(items, 'color', 'customOutput');

            const container = document.getElementById('customButtons');
            container.innerHTML = '';
            buttons.forEach(btn => container.appendChild(btn));

            const segmentInfo = calculateSegments(count);
            const output = document.getElementById('customOutput');
            output.textContent = `生成了 ${count} 个项目的分段按钮\n`;
            output.textContent += `分段策略: ${segmentInfo.segmentCount === 0 ? '只有完整按钮' : `1个完整 + ${segmentInfo.segmentCount}个分段`}\n`;
            output.textContent += `每段项目数: ${segmentInfo.itemsPerSegment || '不适用'}\n\n`;
        };

        window.checkClipboard = async function () {
            try {
                const content = await navigator.clipboard.readText();
                const statusDiv = document.getElementById('clipboardStatus');
                statusDiv.textContent = `当前剪贴板内容:\n━━━━━━━━━━━━━━━━━━━━\n${content}\n━━━━━━━━━━━━━━━━━━━━\n检查时间: ${new Date().toLocaleString()}`;
            } catch (error) {
                document.getElementById('clipboardStatus').textContent = '无法读取剪贴板内容';
            }
        };

        window.clearClipboard = async function () {
            await copyToClipboard('');
            document.getElementById('clipboardStatus').textContent = '剪贴板已清空';
        };

        window.clearOutput = function (id) {
            document.getElementById(id).textContent = '已清除...';
        };

        // 页面加载时初始化预设场景
        window.addEventListener('load', function () {
            // 场景1: 3个颜色值
            const scenario1Items = generateMockItems(3, 'color');
            const scenario1Buttons = createCopyButtonGroup(scenario1Items, 'color', 'scenario1Output');
            const scenario1Container = document.getElementById('scenario1Buttons');
            scenario1Buttons.forEach(btn => scenario1Container.appendChild(btn));

            // 场景2: 12个图片
            const scenario2Items = generateMockItems(12, 'image');
            const scenario2Buttons = createCopyButtonGroup(scenario2Items, 'image', 'scenario2Output');
            const scenario2Container = document.getElementById('scenario2Buttons');
            scenario2Buttons.forEach(btn => scenario2Container.appendChild(btn));

            // 场景3: 25个颜色值
            const scenario3Items = generateMockItems(25, 'color');
            const scenario3Buttons = createCopyButtonGroup(scenario3Items, 'color', 'scenario3Output');
            const scenario3Container = document.getElementById('scenario3Buttons');
            scenario3Buttons.forEach(btn => scenario3Container.appendChild(btn));

            // 场景4: 50个图片
            const scenario4Items = generateMockItems(50, 'image');
            const scenario4Buttons = createCopyButtonGroup(scenario4Items, 'image', 'scenario4Output');
            const scenario4Container = document.getElementById('scenario4Buttons');
            scenario4Buttons.forEach(btn => scenario4Container.appendChild(btn));

            console.log('📊 分段复制功能测试页面已加载');
        });
    </script>
</body>

</html>