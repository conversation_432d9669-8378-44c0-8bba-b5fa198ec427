<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>最少10个内容分组测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-section {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
        }
        .highlight {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .strategy-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        .strategy-table th,
        .strategy-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .strategy-table th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .strategy-table .no-split {
            background-color: #e8f5e8;
        }
        .strategy-table .split {
            background-color: #e8f0ff;
        }
        input[type="number"] {
            width: 80px;
            padding: 5px;
            margin: 0 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .result-display {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📊 最少10个内容分组策略测试</h1>
        <div class="highlight">
            <strong>新规则：</strong>每组中最少要有10个内容
            <ul>
                <li>少于20个项目：不分组，只显示"全部"按钮</li>
                <li>20个及以上：分组，确保每组至少10个内容</li>
                <li>最大分组数限制为8组</li>
            </ul>
        </div>
    </div>

    <div class="container">
        <h2>📋 分组策略表</h2>
        <table class="strategy-table">
            <thead>
                <tr>
                    <th>项目总数</th>
                    <th>分组策略</th>
                    <th>每组数量</th>
                    <th>按钮数量</th>
                    <th>说明</th>
                </tr>
            </thead>
            <tbody>
                <tr class="no-split">
                    <td>1-19个</td>
                    <td>不分组</td>
                    <td>全部</td>
                    <td>1个</td>
                    <td>避免分组后少于10个</td>
                </tr>
                <tr class="split">
                    <td>20-39个</td>
                    <td>2组</td>
                    <td>10-20个/组</td>
                    <td>3个</td>
                    <td>全部 + 2个分组</td>
                </tr>
                <tr class="split">
                    <td>40-59个</td>
                    <td>4组</td>
                    <td>10-15个/组</td>
                    <td>5个</td>
                    <td>全部 + 4个分组</td>
                </tr>
                <tr class="split">
                    <td>60-79个</td>
                    <td>5组</td>
                    <td>12-16个/组</td>
                    <td>6个</td>
                    <td>全部 + 5个分组</td>
                </tr>
                <tr class="split">
                    <td>80个以上</td>
                    <td>8组</td>
                    <td>10+个/组</td>
                    <td>9个</td>
                    <td>全部 + 8个分组</td>
                </tr>
            </tbody>
        </table>
    </div>

    <div class="container">
        <h2>🧪 自定义测试</h2>
        <div class="test-section">
            <div>
                <label>项目数量：</label>
                <input type="number" id="customCount" value="25" min="1" max="200">
                <button onclick="testCustomGrouping()">计算分组策略</button>
                <button onclick="clearResult()">清除结果</button>
            </div>
            
            <div id="customResult" class="result-display">请输入数量并点击"计算分组策略"</div>
        </div>
    </div>

    <div class="container">
        <h2>📊 预设场景测试</h2>
        
        <div class="test-section">
            <h3>场景1：15个项目（不分组）</h3>
            <div class="result-display" id="scenario1">
                总数：15个
                策略：不分组（避免分组后少于10个）
                按钮：[全部]
                标题：全部15个
            </div>
        </div>

        <div class="test-section">
            <h3>场景2：25个项目（2组）</h3>
            <div class="result-display" id="scenario2">
                总数：25个
                策略：2组，每组13个和12个
                按钮：[全部] [1/2] [2/2]
                标题：全部25个 + 13个 + 12个
            </div>
        </div>

        <div class="test-section">
            <h3>场景3：50个项目（5组）</h3>
            <div class="result-display" id="scenario3">
                总数：50个
                策略：5组，每组10个
                按钮：[全部] [1/5] [2/5] [3/5] [4/5] [5/5]
                标题：全部50个 + 10个 + 10个 + 10个 + 10个 + 10个
            </div>
        </div>

        <div class="test-section">
            <h3>场景4：100个项目（8组）</h3>
            <div class="result-display" id="scenario4">
                总数：100个
                策略：8组，每组13个和12个
                按钮：[全部] [1/8] [2/8] [3/8] [4/8] [5/8] [6/8] [7/8] [8/8]
                标题：全部100个 + 13个 + 13个 + 13个 + 13个 + 12个 + 12个 + 12个 + 12个
            </div>
        </div>
    </div>

    <script>
        // 模拟新的分组策略
        function calculateSegments(totalCount) {
            // 如果总数少于20个，不分段
            if (totalCount < 20) {
                return { segmentCount: 0, itemsPerSegment: totalCount };
            }
            
            // 计算最大可能的分段数（确保每段至少10个）
            const maxSegments = Math.floor(totalCount / 10);
            
            // 根据总数量和最大分段数决定实际分段策略
            if (maxSegments >= 8) {
                return { segmentCount: 8, itemsPerSegment: Math.ceil(totalCount / 8) };
            } else if (maxSegments >= 5) {
                return { segmentCount: 5, itemsPerSegment: Math.ceil(totalCount / 5) };
            } else if (maxSegments >= 4) {
                return { segmentCount: 4, itemsPerSegment: Math.ceil(totalCount / 4) };
            } else if (maxSegments >= 3) {
                return { segmentCount: 3, itemsPerSegment: Math.ceil(totalCount / 3) };
            } else if (maxSegments >= 2) {
                return { segmentCount: 2, itemsPerSegment: Math.ceil(totalCount / 2) };
            } else {
                return { segmentCount: 0, itemsPerSegment: totalCount };
            }
        }

        function calculateButtonInfo(totalCount) {
            if (totalCount < 20) {
                return `全部${totalCount}个`;
            }
            
            const segmentInfo = calculateSegments(totalCount);
            const descriptions = [`全部${totalCount}个`];
            
            for (let i = 0; i < segmentInfo.segmentCount; i++) {
                const startIndex = i * segmentInfo.itemsPerSegment;
                const endIndex = Math.min(startIndex + segmentInfo.itemsPerSegment, totalCount);
                const segmentSize = endIndex - startIndex;
                descriptions.push(`${segmentSize}个`);
            }
            
            return descriptions.join(' + ');
        }

        function testCustomGrouping() {
            const count = parseInt(document.getElementById('customCount').value);
            if (count < 1 || count > 200) {
                alert('请输入1-200之间的数字');
                return;
            }
            
            const segmentInfo = calculateSegments(count);
            const buttonInfo = calculateButtonInfo(count);
            
            let result = `项目总数: ${count}个\n`;
            result += `━━━━━━━━━━━━━━━━━━━━\n`;
            
            if (segmentInfo.segmentCount === 0) {
                result += `分组策略: 不分组（少于20个）\n`;
                result += `按钮数量: 1个\n`;
                result += `按钮显示: [全部]\n`;
            } else {
                result += `分组策略: ${segmentInfo.segmentCount}组\n`;
                result += `每组数量: 约${segmentInfo.itemsPerSegment}个\n`;
                result += `按钮数量: ${segmentInfo.segmentCount + 1}个\n`;
                
                const buttons = ['[全部]'];
                for (let i = 1; i <= segmentInfo.segmentCount; i++) {
                    buttons.push(`[${i}/${segmentInfo.segmentCount}]`);
                }
                result += `按钮显示: ${buttons.join(' ')}\n`;
                
                // 显示每组的具体数量
                result += `\n各组详细数量:\n`;
                for (let i = 0; i < segmentInfo.segmentCount; i++) {
                    const startIndex = i * segmentInfo.itemsPerSegment;
                    const endIndex = Math.min(startIndex + segmentInfo.itemsPerSegment, count);
                    const segmentSize = endIndex - startIndex;
                    result += `  第${i + 1}组: ${segmentSize}个 (第${startIndex + 1}-${endIndex}项)\n`;
                }
            }
            
            result += `\n标题显示: ${buttonInfo}`;
            
            document.getElementById('customResult').textContent = result;
        }

        function clearResult() {
            document.getElementById('customResult').textContent = '请输入数量并点击"计算分组策略"';
        }

        // 页面加载时运行预设场景
        window.addEventListener('load', function() {
            // 更新预设场景的实际计算结果
            updateScenario(1, 15);
            updateScenario(2, 25);
            updateScenario(3, 50);
            updateScenario(4, 100);
            
            console.log('📊 最少10个内容分组测试页面已加载');
        });

        function updateScenario(scenarioNum, count) {
            const segmentInfo = calculateSegments(count);
            const buttonInfo = calculateButtonInfo(count);
            
            let result = `总数：${count}个\n`;
            
            if (segmentInfo.segmentCount === 0) {
                result += `策略：不分组（少于20个）\n`;
                result += `按钮：[全部]\n`;
            } else {
                result += `策略：${segmentInfo.segmentCount}组，每组${segmentInfo.itemsPerSegment}个左右\n`;
                
                const buttons = ['[全部]'];
                for (let i = 1; i <= segmentInfo.segmentCount; i++) {
                    buttons.push(`[${i}/${segmentInfo.segmentCount}]`);
                }
                result += `按钮：${buttons.join(' ')}\n`;
            }
            
            result += `标题：${buttonInfo}`;
            
            document.getElementById(`scenario${scenarioNum}`).textContent = result;
        }
    </script>
</body>
</html>
