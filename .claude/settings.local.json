{"permissions": {"allow": ["mcp__ide__getDiagnostics", "Bash(yarn lint)", "Bash(yarn tauri build --debug)", "Bash(npx tsc --noEmit --skipLibCheck src/script/random_color_theme/color_picker.ts)", "Bash(cd /Users/<USER>/gitHub/mini-edit-pro/src-tauri/Terminal)", "Bash(chmod +x tt)", "<PERSON>sh(timeout 10s ./tt \"test1,test2,test3\" 2)", "Bash(./tt --help)", "Bash(chmod +x /Users/<USER>/gitHub/mini-edit-pro/src-tauri/Terminal/test_keypress.sh)", "Bash(chmod +x /Users/<USER>/gitHub/mini-edit-pro/src-tauri/Terminal/improved_keypress.sh)", "Bash(./tt \"test1,test2,test3\" 1)", "Bash(claude mcp add --transport http context7 https://mcp.context7.com/mcp)", "<PERSON><PERSON>(claude mcp list)", "Bash(claude mcp add --transport http kiennd-reference-servers \"https://server.smithery.ai/@kiennd/reference-servers/mcp?api_key=d268cb75-bb52-4720-925e-8d23d80c46f6&profile=little-grandmother-VwTIEs\")", "<PERSON>sh(cd src-tauri)", "Bash(cargo check)", "Bash(cd /Users/<USER>/gitHub/mini-edit-pro/src/script/random_color_theme)", "Bash(mv search_color_tools.ts search_color_tools_original.ts)", "Bash(mv search_color_tools_new.ts search_color_tools.ts)", "Bash(cd /Users/<USER>/gitHub/mini-edit-pro)", "Bash(npx tsc --noEmit)"], "deny": []}}