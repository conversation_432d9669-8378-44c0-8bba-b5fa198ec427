<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TT 命令进度显示测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-section {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
        }
        .output-area {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            min-height: 100px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            white-space: pre-wrap;
            overflow-y: auto;
            max-height: 200px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .progress-demo {
            background: #e9ecef;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        input[type="text"] {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        input[type="number"] {
            width: 80px;
            padding: 5px;
            margin: 0 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .highlight {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 TT 命令进度显示测试</h1>
        <div class="highlight">
            <strong>目标：</strong>确保用户能够实时看到 tt 命令执行到第几个项目了！
        </div>
    </div>

    <div class="container">
        <h2>📊 进度显示组件测试</h2>
        <div class="test-section">
            <h3>1. 模拟 TT 命令输出解析</h3>
            <p>测试进度显示组件是否能正确解析 tt 命令的输出格式</p>
            
            <button onclick="testProgressParsing()">测试进度解析</button>
            <button onclick="clearOutput('parseOutput')">清除输出</button>
            
            <div id="parseOutput" class="output-area">等待测试...</div>
        </div>

        <div class="test-section">
            <h3>2. 实时进度显示测试</h3>
            <p>模拟完整的 tt 命令执行过程，显示实时进度</p>
            
            <div>
                <label>项目列表：</label>
                <input type="text" id="testItems" value="primary_color,secondary_color,accent_color,background_color,text_color" placeholder="用逗号分隔的项目名称">
            </div>
            
            <div>
                <label>首次间隔：</label>
                <input type="number" id="firstInterval" value="1" min="0" max="10">秒
                
                <label style="margin-left: 20px;">其他间隔：</label>
                <input type="number" id="otherInterval" value="0.5" min="0" max="5" step="0.1">秒
            </div>
            
            <div style="margin: 15px 0;">
                <button onclick="startProgressDemo()">开始进度演示</button>
                <button onclick="stopProgressDemo()" id="stopBtn" disabled>停止演示</button>
            </div>
            
            <div id="demoOutput" class="output-area">等待开始...</div>
        </div>
    </div>

    <div class="container">
        <h2>🔧 实际功能测试</h2>
        <div class="test-section">
            <h3>3. 检查页面环境</h3>
            <p>检查当前页面是否具备运行 tt 命令进度显示的条件</p>
            
            <button onclick="checkEnvironment()">检查环境</button>
            <div id="envOutput" class="output-area">等待检查...</div>
        </div>

        <div class="test-section">
            <h3>4. 消息系统测试</h3>
            <p>测试消息提示系统是否正常工作</p>
            
            <button onclick="testMessageSystem()">测试消息系统</button>
            <div id="messageOutput" class="output-area">等待测试...</div>
        </div>
    </div>

    <script type="module">
        // 模拟进度显示组件
        class MockProgressDisplay {
            constructor() {
                this.container = null;
                this.createContainer();
            }

            createContainer() {
                let container = document.getElementById('mock-progress-container');
                if (!container) {
                    container = document.createElement('div');
                    container.id = 'mock-progress-container';
                    container.style.cssText = `
                        position: fixed;
                        top: 20px;
                        right: 20px;
                        background: rgba(0, 0, 0, 0.9);
                        color: white;
                        padding: 15px 20px;
                        border-radius: 8px;
                        font-family: 'Courier New', monospace;
                        font-size: 14px;
                        z-index: 10000;
                        min-width: 300px;
                        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
                        border: 1px solid #333;
                        display: none;
                    `;
                    document.body.appendChild(container);
                }
                this.container = container;
            }

            parseProgressLine(line) {
                const trimmedLine = line.trim();
                
                // 匹配进度格式
                const progressMatch = trimmedLine.match(/\((\d+)\/(\d+)\)\s*([^:]+)(?::\s*(.+))?/);
                if (progressMatch) {
                    return {
                        current: parseInt(progressMatch[1]),
                        total: parseInt(progressMatch[2]),
                        status: progressMatch[3].trim(),
                        item: progressMatch[4]?.trim()
                    };
                }
                
                // 匹配数组模式开始
                const arrayModeMatch = trimmedLine.match(/数组模式:\s*(\d+)个项目/);
                if (arrayModeMatch) {
                    return {
                        current: 0,
                        total: parseInt(arrayModeMatch[1]) + 1,
                        status: '准备开始',
                        item: `${arrayModeMatch[1]}个项目`
                    };
                }
                
                return null;
            }

            updateProgress(progressInfo) {
                if (!this.container) return;
                
                const percentage = progressInfo.total > 0 ? Math.round((progressInfo.current / progressInfo.total) * 100) : 0;
                
                let displayText = `TT 命令执行进度\n`;
                displayText += `━━━━━━━━━━━━━━━━━━━━\n`;
                displayText += `进度: ${progressInfo.current}/${progressInfo.total} (${percentage}%)\n`;
                displayText += `状态: ${progressInfo.status}\n`;
                
                if (progressInfo.item) {
                    displayText += `项目: ${progressInfo.item}\n`;
                }
                
                // 创建进度条
                const barLength = 20;
                const filledLength = Math.round((progressInfo.current / progressInfo.total) * barLength);
                const progressBar = '█'.repeat(filledLength) + '░'.repeat(barLength - filledLength);
                displayText += `[${progressBar}]\n`;
                
                this.container.innerHTML = `<pre style="margin: 0; white-space: pre-wrap;">${displayText}</pre>`;
                this.container.style.display = 'block';
                
                // 输出到测试区域
                const output = document.getElementById('demoOutput');
                if (output) {
                    const timestamp = new Date().toLocaleTimeString();
                    const messageText = progressInfo.item 
                        ? `(${progressInfo.current}/${progressInfo.total}) ${progressInfo.status}: ${progressInfo.item}`
                        : `(${progressInfo.current}/${progressInfo.total}) ${progressInfo.status}`;
                    output.textContent += `[${timestamp}] ${messageText}\n`;
                    output.scrollTop = output.scrollHeight;
                }
            }

            showCompleted(success, message) {
                if (!this.container) return;
                
                if (success) {
                    this.container.style.background = 'rgba(0, 128, 0, 0.9)';
                    this.container.innerHTML = `<pre style="margin: 0;">✅ 操作完成\n${message || '所有项目已处理'}</pre>`;
                } else {
                    this.container.style.background = 'rgba(128, 0, 0, 0.9)';
                    this.container.innerHTML = `<pre style="margin: 0;">❌ 操作失败\n${message || '执行过程中出现错误'}</pre>`;
                }
                
                setTimeout(() => {
                    this.hide();
                }, 3000);
            }

            hide() {
                if (this.container) {
                    this.container.style.display = 'none';
                    this.container.style.background = 'rgba(0, 0, 0, 0.9)';
                }
            }
        }

        // 创建全局实例
        window.mockProgressDisplay = new MockProgressDisplay();
        window.demoRunning = false;
        window.demoAbortController = null;

        // 测试函数
        window.testProgressParsing = function() {
            const output = document.getElementById('parseOutput');
            output.textContent = '开始测试进度解析...\n\n';
            
            const testLines = [
                '数组模式: 5个项目，首次间隔1秒，其余间隔0.5秒',
                '(1/6) 首次: 包含所有值',
                '(2/6) 完成: primary_color',
                '(3/6) 完成: secondary_color',
                '(4/6) 失败: accent_color',
                '(5/6) 完成: background_color',
                '(6/6) 完成: text_color'
            ];
            
            testLines.forEach((line, index) => {
                setTimeout(() => {
                    const progressInfo = window.mockProgressDisplay.parseProgressLine(line);
                    output.textContent += `测试行 ${index + 1}: ${line}\n`;
                    
                    if (progressInfo) {
                        output.textContent += `  ✅ 解析成功: ${JSON.stringify(progressInfo)}\n`;
                        window.mockProgressDisplay.updateProgress(progressInfo);
                    } else {
                        output.textContent += `  ❌ 解析失败\n`;
                    }
                    output.textContent += '\n';
                    output.scrollTop = output.scrollHeight;
                }, index * 800);
            });
        };

        window.startProgressDemo = async function() {
            if (window.demoRunning) return;
            
            const items = document.getElementById('testItems').value.split(',').map(s => s.trim()).filter(s => s);
            const firstInterval = parseFloat(document.getElementById('firstInterval').value) * 1000;
            const otherInterval = parseFloat(document.getElementById('otherInterval').value) * 1000;
            
            if (items.length === 0) {
                alert('请输入项目列表');
                return;
            }
            
            window.demoRunning = true;
            window.demoAbortController = new AbortController();
            document.getElementById('stopBtn').disabled = false;
            
            const output = document.getElementById('demoOutput');
            output.textContent = '开始演示...\n';
            
            try {
                // 开始信息
                const startLine = `数组模式: ${items.length}个项目，首次间隔${firstInterval/1000}秒，其余间隔${otherInterval/1000}秒`;
                const startInfo = window.mockProgressDisplay.parseProgressLine(startLine);
                if (startInfo) {
                    window.mockProgressDisplay.updateProgress(startInfo);
                }
                
                await sleep(firstInterval);
                if (window.demoAbortController.signal.aborted) return;
                
                // 首次批量操作
                const firstInfo = window.mockProgressDisplay.parseProgressLine('(1/' + (items.length + 1) + ') 首次: 包含所有值');
                if (firstInfo) {
                    window.mockProgressDisplay.updateProgress(firstInfo);
                }
                
                // 逐个处理
                for (let i = 0; i < items.length; i++) {
                    if (window.demoAbortController.signal.aborted) return;
                    
                    await sleep(otherInterval);
                    if (window.demoAbortController.signal.aborted) return;
                    
                    const progressLine = `(${i + 2}/${items.length + 1}) 完成: ${items[i]}`;
                    const progressInfo = window.mockProgressDisplay.parseProgressLine(progressLine);
                    if (progressInfo) {
                        window.mockProgressDisplay.updateProgress(progressInfo);
                    }
                }
                
                // 完成
                window.mockProgressDisplay.showCompleted(true, '演示完成！');
                
            } catch (error) {
                if (error.name === 'AbortError') {
                    window.mockProgressDisplay.hide();
                    output.textContent += '演示已中断\n';
                } else {
                    window.mockProgressDisplay.showCompleted(false, '演示出错: ' + error.message);
                }
            } finally {
                window.demoRunning = false;
                document.getElementById('stopBtn').disabled = true;
            }
        };

        window.stopProgressDemo = function() {
            if (window.demoAbortController) {
                window.demoAbortController.abort();
            }
        };

        window.clearOutput = function(id) {
            document.getElementById(id).textContent = '已清除...';
        };

        window.checkEnvironment = function() {
            const output = document.getElementById('envOutput');
            output.textContent = '检查环境...\n\n';
            
            // 检查各种必要的元素和函数
            const checks = [
                { name: 'document.body', test: () => !!document.body },
                { name: 'DOM 操作支持', test: () => !!document.createElement },
                { name: 'setTimeout 支持', test: () => typeof setTimeout === 'function' },
                { name: 'Promise 支持', test: () => typeof Promise === 'function' },
                { name: 'AbortController 支持', test: () => typeof AbortController === 'function' },
                { name: '模块导入支持', test: () => typeof import === 'function' }
            ];
            
            checks.forEach(check => {
                const result = check.test();
                output.textContent += `${result ? '✅' : '❌'} ${check.name}: ${result ? '支持' : '不支持'}\n`;
            });
            
            output.textContent += '\n环境检查完成。';
        };

        window.testMessageSystem = function() {
            const output = document.getElementById('messageOutput');
            output.textContent = '测试消息系统...\n\n';
            
            // 模拟消息显示
            const messages = [
                '正在执行批量操作...',
                '(1/5) 首次: 包含所有值',
                '(2/5) 完成: primary_color',
                '(3/5) 完成: secondary_color',
                '(4/5) 完成: accent_color',
                '(5/5) 完成: background_color'
            ];
            
            messages.forEach((msg, index) => {
                setTimeout(() => {
                    output.textContent += `消息 ${index + 1}: ${msg}\n`;
                    output.scrollTop = output.scrollHeight;
                }, index * 500);
            });
        };

        function sleep(ms) {
            return new Promise((resolve, reject) => {
                const timeoutId = setTimeout(resolve, ms);
                
                if (window.demoAbortController) {
                    const abortHandler = () => {
                        clearTimeout(timeoutId);
                        reject(new Error('AbortError'));
                    };
                    window.demoAbortController.signal.addEventListener('abort', abortHandler);
                }
            });
        }

        // 页面加载完成后的初始化
        console.log('🎯 TT 命令进度显示测试页面已加载');
        console.log('请点击各个测试按钮来验证功能');
    </script>
</body>
</html>
