<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tauri 错误处理测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-section {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
        }
        .output-area {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            min-height: 100px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            white-space: pre-wrap;
            overflow-y: auto;
            max-height: 300px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button.danger {
            background: #dc3545;
        }
        button.danger:hover {
            background: #c82333;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            color: #856404;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            color: #721c24;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            color: #155724;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🛡️ Tauri 错误处理测试</h1>
        <div class="warning">
            <strong>⚠️ 注意：</strong>这个页面用于测试 Tauri 应用中的错误处理机制，特别是回调 ID 错误的处理。
        </div>
    </div>

    <div class="container">
        <h2>🔍 错误检测测试</h2>
        <div class="test-section">
            <h3>1. 回调 ID 错误检测</h3>
            <p>测试是否能正确识别 Tauri 回调 ID 错误</p>
            
            <button onclick="testCallbackIdError()">测试回调 ID 错误检测</button>
            <button onclick="clearOutput('callbackOutput')">清除输出</button>
            
            <div id="callbackOutput" class="output-area">等待测试...</div>
        </div>

        <div class="test-section">
            <h3>2. 连接错误检测</h3>
            <p>测试是否能正确识别 Tauri 连接错误</p>
            
            <button onclick="testConnectionError()">测试连接错误检测</button>
            <button onclick="clearOutput('connectionOutput')">清除输出</button>
            
            <div id="connectionOutput" class="output-area">等待测试...</div>
        </div>
    </div>

    <div class="container">
        <h2>🧪 错误处理器测试</h2>
        <div class="test-section">
            <h3>3. 错误处理器功能</h3>
            <p>测试 TauriErrorHandler 的各种功能</p>
            
            <button onclick="testErrorHandler()">测试错误处理器</button>
            <button onclick="getErrorStats()">获取错误统计</button>
            <button onclick="resetErrorStats()">重置统计</button>
            <button onclick="clearOutput('handlerOutput')">清除输出</button>
            
            <div id="handlerOutput" class="output-area">等待测试...</div>
        </div>
    </div>

    <div class="container">
        <h2>⚡ 实际场景测试</h2>
        <div class="test-section">
            <h3>4. 模拟页面重新加载场景</h3>
            <p>模拟页面重新加载时的回调 ID 错误处理</p>
            
            <div class="warning">
                <strong>测试说明：</strong>这些测试会模拟真实的 Tauri 错误场景，验证错误处理是否正常工作。
            </div>
            
            <button onclick="simulatePageReload()">模拟页面重新加载</button>
            <button onclick="simulateCommandError()">模拟命令执行错误</button>
            <button onclick="testGlobalErrorHandler()">测试全局错误处理</button>
            <button onclick="clearOutput('scenarioOutput')">清除输出</button>
            
            <div id="scenarioOutput" class="output-area">等待测试...</div>
        </div>
    </div>

    <div class="container">
        <h2>📊 错误统计</h2>
        <div class="test-section">
            <div id="errorStats" class="output-area">暂无统计数据</div>
            <button onclick="updateErrorStats()">更新统计</button>
        </div>
    </div>

    <script type="module">
        // 模拟 Tauri 错误处理器
        class MockTauriErrorHandler {
            constructor() {
                this.errorCount = 0;
                this.lastErrorTime = 0;
            }

            isTauriCallbackError(error) {
                const errorStr = String(error);
                return errorStr.includes('callback id') || 
                       errorStr.includes('Couldn\'t find callback') ||
                       errorStr.includes('callback not found');
            }

            isTauriConnectionError(error) {
                const errorStr = String(error);
                return errorStr.includes('connection') ||
                       errorStr.includes('IPC') ||
                       errorStr.includes('backend');
            }

            handleError(error, context = '') {
                const now = Date.now();
                this.errorCount++;
                
                if (this.isTauriCallbackError(error)) {
                    console.warn(`[Tauri] 检测到回调 ID 错误 (${context}):`, error);
                    console.warn('[Tauri] 这通常是由页面重新加载导致的，可以安全忽略');
                    return true;
                }

                if (this.isTauriConnectionError(error)) {
                    console.error(`[Tauri] 检测到连接错误 (${context}):`, error);
                    console.error('[Tauri] 建议检查 Tauri 后端状态');
                    return false;
                }

                console.error(`[Tauri] 未知错误 (${context}):`, error);
                this.lastErrorTime = now;
                return false;
            }

            getErrorStats() {
                return {
                    count: this.errorCount,
                    lastErrorTime: this.lastErrorTime
                };
            }

            resetStats() {
                this.errorCount = 0;
                this.lastErrorTime = 0;
            }
        }

        // 创建全局实例
        window.mockErrorHandler = new MockTauriErrorHandler();

        // 测试函数
        window.testCallbackIdError = function() {
            const output = document.getElementById('callbackOutput');
            output.textContent = '开始测试回调 ID 错误检测...\n\n';
            
            const testErrors = [
                'Couldn\'t find callback id 1541297652',
                'callback id 123456789 not found',
                'Error: callback not found',
                'Some other error message',
                'Connection failed'
            ];
            
            testErrors.forEach((errorMsg, index) => {
                const isCallbackError = window.mockErrorHandler.isTauriCallbackError(errorMsg);
                output.textContent += `测试 ${index + 1}: "${errorMsg}"\n`;
                output.textContent += `  结果: ${isCallbackError ? '✅ 识别为回调错误' : '❌ 不是回调错误'}\n\n`;
            });
            
            output.textContent += '回调 ID 错误检测测试完成。\n';
        };

        window.testConnectionError = function() {
            const output = document.getElementById('connectionOutput');
            output.textContent = '开始测试连接错误检测...\n\n';
            
            const testErrors = [
                'IPC connection failed',
                'backend connection error',
                'connection timeout',
                'Couldn\'t find callback id 123',
                'Some other error'
            ];
            
            testErrors.forEach((errorMsg, index) => {
                const isConnectionError = window.mockErrorHandler.isTauriConnectionError(errorMsg);
                output.textContent += `测试 ${index + 1}: "${errorMsg}"\n`;
                output.textContent += `  结果: ${isConnectionError ? '✅ 识别为连接错误' : '❌ 不是连接错误'}\n\n`;
            });
            
            output.textContent += '连接错误检测测试完成。\n';
        };

        window.testErrorHandler = function() {
            const output = document.getElementById('handlerOutput');
            output.textContent = '开始测试错误处理器...\n\n';
            
            const testCases = [
                { error: 'Couldn\'t find callback id 1541297652', context: 'command execution' },
                { error: 'IPC connection failed', context: 'backend communication' },
                { error: 'Unknown error occurred', context: 'general operation' }
            ];
            
            testCases.forEach((testCase, index) => {
                const shouldIgnore = window.mockErrorHandler.handleError(testCase.error, testCase.context);
                output.textContent += `测试 ${index + 1}: ${testCase.error}\n`;
                output.textContent += `  上下文: ${testCase.context}\n`;
                output.textContent += `  处理结果: ${shouldIgnore ? '✅ 建议忽略' : '❌ 不建议忽略'}\n\n`;
            });
            
            output.textContent += '错误处理器测试完成。\n';
        };

        window.getErrorStats = function() {
            const stats = window.mockErrorHandler.getErrorStats();
            const output = document.getElementById('handlerOutput');
            output.textContent += `\n📊 错误统计:\n`;
            output.textContent += `  错误总数: ${stats.count}\n`;
            output.textContent += `  最后错误时间: ${stats.lastErrorTime ? new Date(stats.lastErrorTime).toLocaleString() : '无'}\n`;
        };

        window.resetErrorStats = function() {
            window.mockErrorHandler.resetStats();
            const output = document.getElementById('handlerOutput');
            output.textContent += '\n🔄 错误统计已重置。\n';
        };

        window.simulatePageReload = function() {
            const output = document.getElementById('scenarioOutput');
            output.textContent = '模拟页面重新加载场景...\n\n';
            
            // 模拟多个回调 ID 错误
            const callbackErrors = [
                'Couldn\'t find callback id 1541297652',
                'Couldn\'t find callback id 1541297653',
                'Couldn\'t find callback id 1541297654'
            ];
            
            callbackErrors.forEach((error, index) => {
                setTimeout(() => {
                    const shouldIgnore = window.mockErrorHandler.handleError(error, 'page reload simulation');
                    output.textContent += `回调错误 ${index + 1}: ${shouldIgnore ? '✅ 已忽略' : '❌ 未忽略'}\n`;
                    
                    if (index === callbackErrors.length - 1) {
                        output.textContent += '\n页面重新加载场景模拟完成。\n';
                    }
                }, index * 500);
            });
        };

        window.simulateCommandError = function() {
            const output = document.getElementById('scenarioOutput');
            output.textContent += '\n模拟命令执行错误...\n';
            
            // 模拟命令执行过程中的各种错误
            const errors = [
                { error: 'Command not found', shouldIgnore: false },
                { error: 'Couldn\'t find callback id 999', shouldIgnore: true },
                { error: 'Permission denied', shouldIgnore: false }
            ];
            
            errors.forEach((testCase, index) => {
                setTimeout(() => {
                    const shouldIgnore = window.mockErrorHandler.handleError(testCase.error, 'command execution');
                    const result = shouldIgnore === testCase.shouldIgnore ? '✅ 正确' : '❌ 错误';
                    output.textContent += `命令错误 ${index + 1}: ${result} (${shouldIgnore ? '忽略' : '不忽略'})\n`;
                    
                    if (index === errors.length - 1) {
                        output.textContent += '\n命令执行错误模拟完成。\n';
                    }
                }, index * 300);
            });
        };

        window.testGlobalErrorHandler = function() {
            const output = document.getElementById('scenarioOutput');
            output.textContent += '\n测试全局错误处理...\n';
            
            // 模拟未捕获的 Promise 拒绝
            const testPromise = new Promise((resolve, reject) => {
                setTimeout(() => {
                    reject(new Error('Couldn\'t find callback id 12345'));
                }, 100);
            });
            
            testPromise.catch(error => {
                const shouldIgnore = window.mockErrorHandler.handleError(error, 'global error handler');
                output.textContent += `全局错误处理: ${shouldIgnore ? '✅ 已处理' : '❌ 未处理'}\n`;
            });
        };

        window.clearOutput = function(id) {
            document.getElementById(id).textContent = '已清除...';
        };

        window.updateErrorStats = function() {
            const stats = window.mockErrorHandler.getErrorStats();
            const statsDiv = document.getElementById('errorStats');
            statsDiv.innerHTML = `
错误统计信息:
━━━━━━━━━━━━━━━━━━━━
总错误数: ${stats.count}
最后错误时间: ${stats.lastErrorTime ? new Date(stats.lastErrorTime).toLocaleString() : '无'}
当前时间: ${new Date().toLocaleString()}

统计说明:
- 总错误数包括所有类型的错误
- 回调 ID 错误通常可以安全忽略
- 连接错误需要特别关注
            `;
        };

        // 页面加载完成后的初始化
        console.log('🛡️ Tauri 错误处理测试页面已加载');
        console.log('请点击各个测试按钮来验证错误处理功能');
        
        // 自动更新统计
        window.updateErrorStats();
    </script>
</body>
</html>
